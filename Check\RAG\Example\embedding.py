# backend/models/implementations/embedding.py
import logging
import tensorflow as tf
import importlib # Added for dynamic imports
import numpy as np
from backend.models.base import ImageModelBase
import torch # Keep for TimmModel and potentially ClipModel if GPU is used
import timm # Keep for TimmModel
from PIL import Image # Keep for TimmModel and add for ClipModel, KerasModel
from timm.data import resolve_data_config, create_transform # Keep for TimmModel
from sentence_transformers import SentenceTransformer # Added for ClipModel

logger = logging.getLogger(__name__)

from transformers import AutoModel, AutoProcessor
from transformers.image_utils import load_image

class SiglipModel(ImageModelBase):
    """
    Image embedding model using Hugging Face transformers with a Siglip model.
    """
    def __init__(self, model_id: str, config: dict):
        """
        Initializes the SiglipModel.

        Args:
            model_id (str): The unique identifier for the model instance.
            config (dict): Configuration parameters. Expected keys:
                                - 'vector_size'
                                - 'model_name_or_path' (e.g., 'google/siglip2-large-patch16-512' or 'google/siglip2-giant-opt-patch16-384')
        """
        super().__init__(config) # Corrected: Pass only config to base
        self.model_id = model_id
        self._vector_size = config.get('vector_size')
        self.model_name_or_path = config.get('model_name_or_path')

        if not self._vector_size:
            raise ValueError(f"Missing required parameter 'vector_size' for SiglipModel {model_id}")
        if not self.model_name_or_path:
            raise ValueError(f"Missing required parameter 'model_name_or_path' for SiglipModel {model_id}")

        self._vector_size = int(self._vector_size)
        self.model = None
        self.processor = None
        # Determine device (prefer CUDA if available, otherwise CPU)
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        logger.info(f"Initialized SiglipModel (ID: {self.model_id}) "
                    f"for Model: {self.model_name_or_path}, Vector Size: {self._vector_size}, Device: {self.device}")

    def load(self):
        """
        Loads the Siglip model and processor.
        """
        if self.model is not None and self.processor is not None:
            logger.info(f"Model {self.model_id} ({self.model_name_or_path}) is already loaded.")
            return

        try:
            logger.info(f"Loading Siglip model: {self.model_name_or_path} to device: {self.device}")
            # Load the model and processor
            self.model = AutoModel.from_pretrained(
                self.model_name_or_path, trust_remote_code=True # Add trust_remote_code
            ).eval().to(self.device)
            self.processor = AutoProcessor.from_pretrained(
                self.model_name_or_path, use_fast=True, trust_remote_code=True # Add trust_remote_code
            )
            logger.info(f"Successfully loaded model {self.model_id} ({self.model_name_or_path})")

        except Exception as e:
            logger.error(f"Error loading model {self.model_id} ({self.model_name_or_path}): {e}", exc_info=True)
            self.model = None
            self.processor = None
            raise

    def unload(self):
        """
        Unloads the Siglip model and processor.
        """
        if self.model is not None or self.processor is not None:
            logger.info(f"Unloading model {self.model_id} ({self.model_name_or_path})...")
            self.model = None
            self.processor = None
            if torch.cuda.is_available():
                torch.cuda.empty_cache() # Clear GPU cache if applicable
            logger.info(f"Model {self.model_id} unloaded.")
        else:
            logger.info(f"Model {self.model_id} is not loaded, nothing to unload.")

    def get_model_type(self) -> str:
        return 'embedding'

    @property
    def vector_size(self) -> int:
        if self._vector_size is None:
             raise ValueError(f"Vector size not set for model {self.model_id}")
        return self._vector_size

    def preprocess(self, image_path: str):
        """
        Loads and preprocesses an image using the Siglip processor.
        Converts image to RGB to handle grayscale inputs.
        """
        if self.processor is None:
             raise ValueError(f"Model {self.model_id} processor not initialized. Load the model first.")

        try:
            logger.debug(f"Preprocessing image for {self.model_name_or_path}: {image_path}")
            # Load image using transformers' utility and convert to RGB
            image = load_image(image_path).convert("RGB")
            # Process the image
            inputs = self.processor(images=[image], return_tensors="pt").to(self.device)

            logger.debug(f"Preprocessing complete for {image_path}. Output shape: {inputs['pixel_values'].shape}")
            return inputs

        except FileNotFoundError as e:
             logger.error(f"Image file not found at path: {image_path}")
             raise
        except Exception as e:
            logger.error(f"Unexpected error during preprocessing of {image_path}: {e}", exc_info=True)
            raise

    def compute_features(self, preprocessed_image) -> np.ndarray:
        """
        Computes the feature vector for the preprocessed image using the Siglip model.
        """
        if self.model is None:
            logger.error(f"Model {self.model_id} ({self.model_name_or_path}) is not loaded. Cannot compute features.")
            raise ValueError(f"Model {self.model_id} must be loaded before computing features.")

        try:
            logger.debug(f"Computing features for input")
            # Ensure input tensor is on the correct device
            # preprocessed_image = preprocessed_image.to(self.device) # Already moved in preprocess

            with torch.no_grad(): # Disable gradient calculation for inference
                # Siglip uses get_image_features
                features = self.model.get_image_features(**preprocessed_image)

            # Move features to CPU and convert to NumPy array
            features_np = features.cpu().numpy()
            features_np = np.squeeze(features_np) # Remove batch dim if size 1

            if features_np.ndim > 0 and features_np.shape[0] != self.vector_size:
                 logger.warning(f"Model {self.model_id} ({self.model_name_or_path}) produced vector of size {features_np.shape[0]}, "
                                 f"but expected {self.vector_size}. Check model/config.")
                 # Consider raising an error

            logger.debug(f"Computed features shape: {features_np.shape}")
            return features_np

        except Exception as e:
            logger.error(f"Error computing features with model {self.model_id} ({self.model_name_or_path}): {e}", exc_info=True)
            raise

    def normalize_score(self, raw_score: float) -> float:
        """
        Normalizes cosine similarity score. Assumes raw_score is similarity in [-1, 1].
        Maps it to [0, 1].
        """
        # Cosine similarity is in [-1, 1]. Map to [0, 1] where 1 is most similar.
        normalized = (float(raw_score) + 1.0) / 2.0
        normalized = max(0.0, min(1.0, normalized)) # Clamp to [0, 1]
        logger.debug(f"Normalizing raw score {raw_score} to {normalized}")
        return normalized