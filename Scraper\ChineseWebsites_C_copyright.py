#nofloqa
import json, os, sys, mimetypes, shutil
from PIL import Image # Pillow imports (ExifTags not needed)
import piexif # Added piexif import for EXIF manipulation
import glob
import json
sys.path.append(os.getcwd())
from AI.GC_VertexAI import vertex_genai_image_gen_async
from AI.GCV_GetImageParts import get_image_parts_async
import Common.Constants as Constants
from langfuse.decorators import observe, langfuse_context
from Scraper import ChineseWebsites_C_copyright_google, ChineseWebsites_C_copyright_serpapi, ChineseWebsites_C_copyright_tineye
from Scraper.ChineseWebsites_C_copyright_image_deduplicator import deduplicate_images
from logdata import log_message
from typing import List, Dict, Any

# ❌⚠️📥🔥✅☑️✔️💯💧⛏🔨🗝🔑 🔒💡⛔🚫❗


# Step C: process the copyrighted images

@observe()
async def C_copyright_process_picture(
    c_input_item: Dict[str, Any], # The dictionary containing all context for this item
    copyright_case_base_dir: str,
    watermarked_dir, reverse_search_output_dir, reverse_search_final_dir,
    duplicate_dir: str # This is the duplicate_dir specific to C_step processing
) -> List[Dict[str, str]]:
    """
    Processes a single image (original or split part).
    Copies the image from download_path, adds metadata,
    removes watermark using GenAI (or uses reverse search), saves clean version to copyright_case_base_dir.
    """
    # Extract necessary fields from c_input_item
    reg_no: str = c_input_item["reg_no"]
    img_url: str = c_input_item["img_url"]
    source_page_url: str = c_input_item["source_page_url"]
    source_site: str = c_input_item["source_site"]
    watermark_description: str = c_input_item["watermark_description"]
    download_path: str = c_input_item["download_path"] # Path from copyright_cn_allpictures_dir

    try:
        # download_path is the image from copyright_cn_allpictures_dir (already deduplicated globally)
        # We first copy it to watermarked_dir for this specific C_step processing.
        original_extension = os.path.splitext(download_path)[-1]
        watermark_path_in_c_step = os.path.join(watermarked_dir, f"{Constants.sanitize_name(reg_no)}_original{original_extension}")

        shutil.copy(download_path, watermark_path_in_c_step)
        # log_message(f"      C_step: Copied image {download_path} -> {watermark_path_in_c_step}")

        # Convert to JPG if needed (for EXIF)
        watermark_path_in_c_step = convert_to_jpg(watermark_path_in_c_step, reg_no)
        file_extension = os.path.splitext(watermark_path_in_c_step)[-1] # Get extension after potential conversion

        # Add metadata
        add_metadata(watermark_path_in_c_step, source_page_url, source_site, img_url)

        # Deduplicate within the context of images being processed by C_copyright_process_picture
        # This checks if we've already processed an identical image (e.g. from a different source URL but same content)
        # in *this current run* of C_copyright_process_picture calls.
        deduplicate_images(watermarked_dir, duplicate_dir)

        if not os.path.exists(watermark_path_in_c_step):
            log_message(f"      C_step: Image {reg_no} ({watermark_path_in_c_step}) was moved to duplicates by C_step deduplication. Skipping further processing.")
            # Check if a final version already exists for this reg_no from a previous C_step call in this scrape_case_data run
            existing_finals_for_reg_no = glob.glob(os.path.join(copyright_case_base_dir, f"{Constants.sanitize_name(reg_no)}*"))
            if existing_finals_for_reg_no:
                log_message(f"      C_step: Found existing processed images for duplicate {reg_no} in final directory: {existing_finals_for_reg_no}")
                return [{"reg": reg_no, "image_path": path} for path in existing_finals_for_reg_no]
            else:
                log_message(f"      C_step: No existing processed images found for duplicate {reg_no} after C_step deduplication.")
                return []

        final_image_paths = []
        base_name = Constants.sanitize_name(reg_no)

        # Overall check: if a final version for this reg_no already exists in copyright_case_base_dir, skip.
        existing_finals_overall = glob.glob(os.path.join(copyright_case_base_dir, f"{base_name}*"))
        # Ensure we are only looking at files directly in copyright_case_base_dir, not in its subdirectories.
        # This check is to prevent reprocessing if the script is run multiple times or an identical reg_no was processed.
        existing_finals_in_final_dir = [
            f for f in existing_finals_overall
            if os.path.isfile(f) and os.path.dirname(f) == os.path.abspath(copyright_case_base_dir)
        ]
        if existing_finals_in_final_dir:
            log_message(f"      C_step: Found existing processed images for {reg_no} in final directory (overall check): {existing_finals_in_final_dir}")
            return [{"reg": reg_no, "image_path": path} for path in existing_finals_in_final_dir]

        # Create reg_no specific subdirectories for this reverse search instance
        # This ensures that the results of one reverse search don't interfere with another.
        current_reverse_search_output_dir = os.path.join(reverse_search_output_dir, base_name)
        os.makedirs(current_reverse_search_output_dir, exist_ok=True)
        current_reverse_search_final_dir = os.path.join(reverse_search_final_dir, base_name)
        os.makedirs(current_reverse_search_final_dir, exist_ok=True)

        # log_message(f"👀 C_step: Performing Serpapi reverse image search for {watermark_path_in_c_step} with URL: {img_url}")
        # reverse_search_results2 = ChineseWebsites_C_copyright_serpapi.reverse_search_and_download( # Result not directly used, relies on file output
        #     watermark_path_in_c_step,
        #     image_url=img_url,  # Pass the original image URL
        #     output_folder=current_reverse_search_output_dir,   # Use scoped output directory
        #     final_folder=current_reverse_search_final_dir,     # Use scoped final directory
        #     max_images=10
        # )
        
        log_message(f"👀 C_step: Performing TinEyereverse image search for {watermark_path_in_c_step}")
        reverse_search_results = ChineseWebsites_C_copyright_tineye.reverse_search_and_download( # Result not directly used, relies on file output
            watermark_path_in_c_step,
            output_folder=current_reverse_search_output_dir,   # Use scoped output directory
            final_folder=current_reverse_search_final_dir,     # Use scoped final directory
            max_images=5
        )
        
        # If TinEYE found something we take it
        if len(reverse_search_results) > 0:
            reverse_search_results.sort(key=lambda x: x.get("similarity", 0.0), reverse=True)
            high_similarity_image_path = reverse_search_results[0].get("path")
            dest_filename = f"{base_name}_reverse_search{os.path.splitext(high_similarity_image_path)[1]}"
            dest_path = os.path.join(copyright_case_base_dir, dest_filename)
            shutil.copy2(high_similarity_image_path, dest_path)
            final_image_paths.append({"reg": reg_no, "image_path": dest_path})
        else: # If TinEYE found nothing, we use google
            log_message(f"👀 C_step: Performing Google reverse image search for {watermark_path_in_c_step}")
            reverse_search_results = ChineseWebsites_C_copyright_google.reverse_search_and_download( # Result not directly used, relies on file output
                watermark_path_in_c_step,
                output_folder=current_reverse_search_output_dir,   # Use scoped output directory
                final_folder=current_reverse_search_final_dir,     # Use scoped final directory
                max_images=5
            )
            
            high_similarity_image_path = None
            highest_similarity_score = 0.0 # For the current image's specific reverse search

            if reverse_search_results: # Check if the list is not empty
                # Sort by similarity, highest first, in case multiple images met the threshold
                reverse_search_results.sort(key=lambda x: x.get("similarity", 0.0), reverse=True)
                best_match_from_reverse = reverse_search_results[0] # Get the top one
                highest_similarity_score = best_match_from_reverse.get("similarity", 0.0)
                high_similarity_image_path = best_match_from_reverse.get("path")
                log_message(f"      C_step: Best match from reverse search for {base_name}: similarity={highest_similarity_score}, path={high_similarity_image_path}")
            else:
                log_message(f"      C_step: No results returned from reverse search for {base_name}.")


            log_message(f"      C_step: Best reverse search match for {base_name} (from its own search): similarity={highest_similarity_score}, path={high_similarity_image_path}")

            if highest_similarity_score > 0.4 and high_similarity_image_path and os.path.exists(high_similarity_image_path):
                log_message(f"      C_step: Using high-similarity match ({highest_similarity_score}) for {base_name} from its own reverse search: {high_similarity_image_path.replace(os.getcwd(), '')}")
                dest_filename = f"{base_name}_reverse_search{os.path.splitext(high_similarity_image_path)[1]}"
                dest_path = os.path.join(copyright_case_base_dir, dest_filename)

                if not os.path.exists(dest_path):
                    shutil.copy2(high_similarity_image_path, dest_path)
                    log_message(f"      C_step: Using high-similarity image from reverse search: {dest_path.replace(os.getcwd(), '')}")
                else:
                    log_message(f"      C_step: High-similarity image already exists in final directory: {dest_path.replace(os.getcwd(), '')}")
                final_image_paths.append({"reg": reg_no, "image_path": dest_path})
            else: # if google found nothing good, use GenAI
            # if True:
                # Use GenAI for watermark removal
                log_message(f"      💡 Reverse search for {base_name} did not yield a match > 0.4 (score: ....) => Using GenAI for watermark removal.")
                watermark_removal_prompt = f"Your job is to generate a picture exactly the same but remove the {watermark_description}"

                # Check if a GenAI version already exists in the final directory (copyright_case_base_dir)
                genai_pattern = os.path.join(copyright_case_base_dir, f"{base_name}_genai*")
                existing_genai_in_final_dir = [
                    f for f in glob.glob(genai_pattern)
                    if os.path.isfile(f) and os.path.dirname(f) == os.path.abspath(copyright_case_base_dir)
                ]

                if existing_genai_in_final_dir:
                    log_message(f"      C_step: Found existing GenAI processed image for {reg_no}: {existing_genai_in_final_dir[0]}")
                    final_image_paths.append({"reg": reg_no, "image_path": existing_genai_in_final_dir[0]})
                else:
                    inline_data = await vertex_genai_image_gen_async(
                        [("text", watermark_removal_prompt), ("image_path", watermark_path_in_c_step)]
                    )
                    if inline_data and not isinstance(inline_data, str):
                        processed_image_bytes = inline_data.data
                        mime_type = inline_data.mime_type
                        genai_extension = mimetypes.guess_extension(mime_type) or f".{mime_type.split('/')[-1]}"
                        final_image_path_genai = os.path.join(copyright_case_base_dir, f"{base_name}_genai{genai_extension}")

                        try:
                            with open(final_image_path_genai, "wb") as f:
                                f.write(processed_image_bytes)
                            log_message(f"      C_step: Saved watermark-removed image for {reg_no} to {final_image_path_genai}")
                            final_image_paths.append({"reg": reg_no, "image_path": final_image_path_genai})
                        except Exception as save_err:
                            log_message(f"      C_step: Error saving watermark-removed image for {reg_no}: {save_err}")
                            # Fallback: if GenAI fails, use original image
                            orig_final_path = os.path.join(copyright_case_base_dir, f"{base_name}_original{file_extension}")
                            # Check if original already exists in final_dir
                            if not os.path.exists(orig_final_path):
                                shutil.copy2(watermark_path_in_c_step, orig_final_path)
                                log_message(f"      C_step: Fallback to original image: {orig_final_path}")
                            else:
                                log_message(f"      C_step: Fallback to Original image already exists in final directory: {orig_final_path}")
                            final_image_paths.append({"reg": reg_no, "image_path": orig_final_path})
                    else:
                        log_message(f"      C_step: GenAI did not return image data for {reg_no}. Using original image as fallback.")
                        orig_final_path = os.path.join(copyright_case_base_dir, f"{base_name}_original{file_extension}")
                        if not os.path.exists(orig_final_path):
                            shutil.copy2(watermark_path_in_c_step, orig_final_path)
                            log_message(f"      C_step: Fallback to original image: {orig_final_path}")
                        else:
                            log_message(f"      C_step: Original image already exists in final directory: {orig_final_path}")
                        final_image_paths.append({"reg": reg_no, "image_path": orig_final_path})

        # Fallback if no paths were added (e.g., all processing failed or skipped due to existing files)
        if not final_image_paths:
            log_message(f"      C_step: No processed images generated for {reg_no}, using original from watermarked_dir as fallback.")
            orig_final_path = os.path.join(copyright_case_base_dir, f"{base_name}_original{file_extension}")
            if not os.path.exists(orig_final_path):
                shutil.copy2(watermark_path_in_c_step, orig_final_path)
            else:
                 log_message(f"      C_step: Original image (fallback) already exists in final directory: {orig_final_path}")
            final_image_paths.append({"reg": reg_no, "image_path": orig_final_path})

        # Remove duplicates from final_image_paths by path
        unique_paths = set()
        unique_final_image_paths = []
        for item in final_image_paths:
            if item["image_path"] not in unique_paths:
                unique_paths.add(item["image_path"])
                unique_final_image_paths.append(item)
        
        log_message(f"      C_step: Final images for {reg_no}: {[path['image_path'].replace(os.getcwd(), '') for path in unique_final_image_paths]}")
        return unique_final_image_paths

    except Exception as e:
        log_message(f"      C_step: Error during C_copyright_process_picture for {reg_no}: {e}", level="ERROR")
        # Clean up potentially incomplete original download if it exists and an error occurred
        if 'watermark_path_in_c_step' in locals() and os.path.exists(watermark_path_in_c_step) and 'final_image_path_genai' not in locals() and 'dest_path' not in locals() : # Only delete if processing failed before final save
             try:
                 os.remove(watermark_path_in_c_step)
                 log_message(f"      C_step: Removed incomplete watermarked file: {watermark_path_in_c_step}")
             except OSError as rm_err:
                 log_message(f"      C_step: Error removing incomplete watermarked file {watermark_path_in_c_step}: {rm_err}")

        # In case of error, try to return original image if available
        if 'watermark_path_in_c_step' in locals() and os.path.exists(watermark_path_in_c_step):
            dest_filename_err = f"{Constants.sanitize_name(reg_no)}_error_original{os.path.splitext(watermark_path_in_c_step)[1]}"
            dest_path_err = os.path.join(copyright_case_base_dir, dest_filename_err)
            try:
                # Only copy if it doesn't already exist
                if not os.path.exists(dest_path_err):
                    shutil.copy2(watermark_path_in_c_step, dest_path_err)
                    log_message(f"      C_step: Error recovery: saved original image to {dest_path_err}")
                else:
                    log_message(f"      C_step: Error recovery: original image already exists at {dest_path_err}")
                return [{"reg": reg_no, "image_path": dest_path_err}]
            except Exception as copy_err:
                log_message(f"      C_step: Error during recovery copy: {copy_err}")
        
        return []


def convert_to_jpg(watermark_path, reg_no):
    # 1. Check image format and convert if necessary before adding metadata
    try:
        with Image.open(watermark_path) as img:
            img_format = img.format.upper() # Get format (JPEG, PNG, etc.)
            if img_format not in ['JPEG', 'TIFF']:
                print(f"Original format {img_format} not supported by piexif. Converting {reg_no} to JPEG.")
                # Ensure image is in RGB mode for JPEG saving
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Create new path for JPEG version
                base, _ = os.path.splitext(watermark_path)
                new_jpeg_path = f"{base}.jpg"
                
                # Save as JPEG
                img.save(new_jpeg_path, "JPEG")
                print(f"Saved converted JPEG to: {new_jpeg_path}")
                
                # Remove original non-JPEG/TIFF file
                try:
                    if watermark_path != new_jpeg_path:
                        os.remove(watermark_path)
                except OSError as rm_err:
                    log_message(f"Warning: Could not remove original file {watermark_path} after conversion: {rm_err}")
                    
                # Update path to point to the new JPEG file
                watermark_path = new_jpeg_path
            # If no conversion was done, the original path is still valid.
            return watermark_path
                
    except FileNotFoundError:
            log_message(f"      Error: Downloaded file not found at {watermark_path} before format check.")
            raise # Re-raise the error to be caught by the outer try-except
    except Exception as img_err:
        log_message(f"      Error during image format check/conversion for {reg_no}: {img_err}")
        raise # Re-raise to be caught by outer try-except

def add_metadata(watermark_path, source_page_url, source_site, img_url):
    # 2. Add Metadata to Original File (now guaranteed to be JPEG/TIFF or skipped)
    metadata = {
        "source_site": source_site,
        "source_page_url": source_page_url,
        "original_image_url": img_url,
        "processing_step": "original_download" # Indicate this is the original
    }
    metadata_json = json.dumps(metadata)
    try:
        # Load original image's EXIF, add comment, insert back into file
        exif_dict = piexif.load(watermark_path)
        comment_bytes = metadata_json.encode('utf-8')
        exif_dict["Exif"][piexif.ExifIFD.UserComment] = comment_bytes
        exif_bytes = piexif.dump(exif_dict)
        piexif.insert(exif_bytes, watermark_path) # Insert EXIF into the original file
        # log_message(f"Added metadata to original file: {watermark_path}")
    except Exception as meta_err:
        # If EXIF loading/insertion fails (e.g., non-JPEG), we skip adding metadata but continue
        log_message(f"Warning: Could not add metadata to original file {watermark_path}: {meta_err}")
