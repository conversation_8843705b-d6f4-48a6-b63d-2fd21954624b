1. Do not search VAu on google! it is unpublished!
2. Are we missing the plaintiff overview and translation?
3. We delete the image folder, then use "resume mode" then upload the folder to NAS. Are we loosing the images from chinese website?
4. In a given step, if all the PDFs are local already, we open the download modal 3 times!
5. Platform to review copyright and approave pics by pics
6. For scrape_date_range: we need to save the cases that do not meet the conditions? Do that we dont need to open the page for nothing?

When save_to_db is false, we are still inserting data into database for : inserting new case (get ID) and new plaintiff?

resume mode:

- still sending pictures to COS and into
- steps_df:
  - files_downloaded and failed get reinit to 0
  - all translation is redone
  - case_id is missing

steps_df.loc[steps_df['row_index'] == target_step_row_index, 'files_failed'] += 1 - num_downloaded  # Expecting 1 main file  : This is for steps with exhibit. How about if they have multiple files? The result will be negative

Merge process_copyright_regno and process_copyright_registration_numbers, and same for trademark and patent

suggestion on the 1+Deep improvement. download the exhibit when there is "unredacted". even though there is a "sealed"
Sealed: sometimes: the sealed steps has some documents available
Sometime the Answer is "Motion by Defandant"
Sometimes the IP is in exhibits to the "Motion by Defandant"

**Trademark by name:**

- When we have multiple Owners on USPTO we could ask the AI which one it is

**Copyright by name:**

I see 2 problems:

1. We save the data scaped from UCO in the database. Suposedly so that next time, we can source it from the database (using the plaintiff_ids field which is a list of plaintiff_ids) instead of searching on UCO.
2. When we save the fact that image was found on google inside the database, we then dont use that info later to avoid redoing the google search.

**Fix this:**
case ID 13677, 1:25-cv-04962...
INFO: Relevant steps to be considered:
    step_nb               priority_name                                    proceeding_text
1     2.00          PRIORITY_COMPLAINT  COMPLAINT against 2foxieh, ABQP WHHR Bags Stor...
26   25.00  PRIORITY_AMENDED_COMPLAINT  AMENDED COMPLAINT against 2FOXIEH, ABQP WHHR B...
32   31.00                PRIORITY_TRO  MOTION to Dissolve Temporary Restraining Order...
2     3.00  PRIORITY_PRELIM_INJUNCTION  MOTION for Temporary Restraining Order, MOTION...

**Need to build:**

a function to remove from COS images not associated with cases anymore

a function to remove from database (or turn TRO to False) copyright/patent/trademark that are not associated with cases anymore

**Path:**
Main doc => relevant and reg_no
    Trademark reg_no whole_set => get from USPTO
    Patent reg_no whole_set => get from USPTO
    Copyright target main doc is True and target exhibit is emoty => process_copyright_regno
    Get exhibit (either mentionned or all)
        Trademark: Objective: get the whole_set of reg_no using LLM => get from USPTO
            if cannot find on USPTO -> extract form Exhibit (need to get rid of that!)
        Patent: Objective: get the whole_set of reg_no using LLM => get from USPTO
        Copyright: try to extract images
    Chinese Websites:

    By name:

Note:
process_copyright_regno =
    search for picture inside the provided PDF, e.g. as a table
    if not found:
        get the LLM to extract the artist and title
        if not found:
            get artist and title on USCO
        Search google
