#nofloqa
import re, json, aiohttp, os, sys
import json
sys.path.append(os.getcwd())
from bs4 import BeautifulSoup
from markdownify import markdownify
from AI.GC_VertexAI import vertex_genai_multi_async
from AI.LLM_shared import get_json
from Check.Do_Check_Download import download_from_url # Keep existing correct import
import Common.Constants as Constants
from langfuse.decorators import observe, langfuse_context
from logdata import log_message
from Scraper.ChineseWebsites_utils import fetch_url, WEBSITE_CONFIG
# ❌⚠️📥🔥✅☑️✔️💯💧⛏🔨🗝🔑 🔒💡⛔🚫❗


# Step B: extract the Reg Numbers and Copyright images from the page
@observe(capture_input=False, capture_output=False)
async def B_extract_data_from_url(session: aiohttp.ClientSession, url: str, site_name: str, docket: str, plaintiff_name: str, date_filed: object, case_folder: str, copyright_cn_allpictures_dir: str) -> tuple[dict | None, dict]:
    """
    Fetches a target URL, converts to Markdown, processes images (downloading based on config),
    sends to LLM, and returns extracted data along with source info and a map of downloaded images.

    Args:
        session: The aiohttp client session.
        url: The URL of the page to process.
        site_name: The name of the website (used to get config).
        docket: The case docket number.
        date_filed: The date the case was filed (used for constructing download paths).
        case_folder: The pre-defined folder name for the case.

    Returns:
        A tuple: (json_response, downloaded_images_map)
        - json_response: The dictionary parsed from the LLM response, or None on error.
        - downloaded_images_map: A dictionary mapping {temp_persistent_path: original_image_url} for images downloaded.
    """
    downloaded_images_map = {} # {temp_persistent_path: original_image_url}
    log_message(f"      Fetching data from: {url} (Site: {site_name})")
    content = await fetch_url(session, url)
    if not content:
        return None, downloaded_images_map

    soup = BeautifulSoup(content, 'html.parser')

    # Retrieve config for the current site
    site_config = next((config for config in WEBSITE_CONFIG if config["name"] == site_name), None)
    content_selector = None
    image_download_pattern = None
    if site_config:
        content_selector = site_config.get("content_selector")
        image_download_pattern = site_config.get("image_download_pattern")

    # Determine the HTML content to convert to Markdown
    html_to_convert = ""
    if content_selector:
        selected_element = soup.select_one(content_selector)
        if selected_element:
            html_to_convert = str(selected_element)
        else:
            log_message(f"      Warning: Content selector '{content_selector}' not found for {url}. Falling back to body.")
            html_to_convert = str(soup.body) if soup.body else str(soup)
    else:
        html_to_convert = str(soup.body) if soup.body else str(soup) # Fallback to whole body if no selector

    # Convert the relevant HTML part to Markdown
    markdown_content = markdownify(html_to_convert, heading_style="ATX", bullets='*', body_width=0)

    # --- Image Processing and Input List Construction ---
    prompt_input_list = []

    last_end = 0
    image_index = 0
    # Regex to find Markdown images: ![alt text](url)
    for match in re.finditer(r'!\[.*?\]\((.*?)\)', markdown_content):
        # Append text chunk before the image
        text_chunk = markdown_content[last_end:match.start()]
        if text_chunk: prompt_input_list.append(("text", text_chunk))

        full_image_url = match.group(1)
        
        # If (pattern not set) or (pattern is set & found)
        if (not image_download_pattern or re.search(image_download_pattern, full_image_url)) and "data:image/" not in full_image_url and "lazy.png" not in full_image_url:
            original_filename = full_image_url.split("?")[0].split("/")[-1] # Get the last part of the URL before any query string
            temp_persistent_path = os.path.join(copyright_cn_allpictures_dir, original_filename)
            if not os.path.exists(temp_persistent_path):
                await download_from_url(full_image_url, temp_persistent_path)
                
            if os.path.exists(temp_persistent_path):
                # If download succeeded (or file already existed), add to prompt and map
                if os.path.exists(temp_persistent_path):
                    image_index += 1
                    prompt_input_list.append(("text", f"\n[Provided Image Identifier: Image_{image_index}]\n"))
                    prompt_input_list.append(("image_path", temp_persistent_path))
                    downloaded_images_map[f"Image_{image_index}"] = (temp_persistent_path, full_image_url) # Still useful for metadata

        last_end = match.end()

    # Append the final text chunk
    final_text_chunk = markdown_content[last_end:]
    if final_text_chunk: prompt_input_list.append(("text", final_text_chunk))

    # Filter out empty text items
    prompt_input_list = [item for item in prompt_input_list if item[0] != "text" or item[1].strip()]

    # --- End Image Processing ---

    # Prepare the LLM prompt (Instruction part)
    prompt = f"""
Analyze the following content, which consists of text and images, extracted from the webpage {url}.
Identify and extract all intellectual property registration numbers mentioned related to legal case {docket} filed by plaintiff {plaintiff_name}.
Specifically look for:
1.  Trademark Registration Numbers (usually start with digits or contain specific keywords like "Trademark Reg. No.")
2.  Patent Registration Numbers (often start with "US" followed by digits, or contain keywords like "Patent No.")
3.  Copyright Registration Numbers (often start with "VA", "TX", "SR", or contain keywords like "Copyright Reg. No.")

For copyright you also provide the image identifier of the image that appear to be related to Copyright registrations. Try to associate each copyright image with a specific Copyright Registration Number if possible. If an image is present but no clear registration number is nearby, list the image URL with a placeholder key like "no_reg_X". If a registration number is found with no associated image, list it with a null value for the image URL.
Additionally, check the page (especially near copyright information) for a single URL pointing to the artist's main website or portfolio (artist_url). If found, include it as a top-level key in the JSON.

Base your analysis *only* on the text provided and the content of the images. Do not infer or invent information (such as registration numbers) that is not explicitly present in the text or visible in the images.

Return the extracted information ONLY as a JSON object with the following structure:
{{
  "trademarks": ["list of strings"],
  "patents": ["list of strings"],
  "copyrights": [
    {{ "reg_no": "VAx123456", "identifier": "image_identifier_a"}},
    {{ "reg_no": "VAx234567", "identifier": null}},
    {{ "reg_no": "no_reg_1", "identifier": "image_identifier_b"}},
    {{ "reg_no": "multi", "identifier": "image_identifier_c"}}
  ],
  "artist_url": "url_string_or_null"
}}
where
- "copyrights": A list of JSON objects. Each object should have a "reg_no" key (string, for the registration number, or a placeholder like "no_reg_1", or "multi" if the image has multiple copyrighted images in a single image) and an "identifier" key (string, for the image identifier found in the markdown, or null if no image is associated).
- "artist_url": Contains the single artist website URL found on the page, or null if none was found.

If one of the image has a table with text and no copyrighted pictures, do not include it.

Do not include any introductory text, explanations, or markdown formatting in your response. Just the JSON object.
"""
    # Construct the final input for the LLM
    llm_input = [("text", prompt)] + prompt_input_list

    # Make the LLM call
    response = await vertex_genai_multi_async(llm_input, model_name=Constants.TEXT_MODEL_FREE_LIMITED, useVertexAI=Constants.TEXT_MODEL_FREE_LIMITED_VERTEX) # gemini-2.5-pro-exp-03-25

    # Process LLM response
    try:
        json_response = get_json(response)
        # Add source information to the result
        if isinstance(json_response, dict):
            json_response["source_page_url"] = url
            json_response["source_site"] = site_name
    except json.JSONDecodeError:
        log_message(f"      Error decoding LLM response for {url}: {response}")
        json_response =  None
    except Exception as e:
        log_message(f"      Unexpected error processing LLM response for {url}: {e}")
        json_response = None

    langfuse_context.update_current_observation(
        input={"URL": url, "SiteName": site_name},
        output={"LLMResponse": json_response if json_response is not None else 'None/Error'}
    )
    
    return json_response, downloaded_images_map # Return map along with response,  Still return map even if LLM fails
