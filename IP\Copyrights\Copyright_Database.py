import time
import random
import json # Added for URL building
import os
import urllib.parse
import undetected_chromedriver as uc
import pandas as pd
import re
from typing import List

from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import StaleElementReferenceException, TimeoutException, NoSuchElementException
from typing import Optional

from Alerts.Chrome_Driver import get_driver, move_mouse_to, random_delay, driver_is_alive
from AI.GC_VertexAI import vertex_genai_multi
from AI.LLM_shared import get_json
from typing import Any
from logdata import log_message
from DatabaseManagement.Connections import get_gz_connection
from DatabaseManagement.ImportExport import insert_and_update_df_to_GZ_batch

# ❌⚠️📥🔥✅


def prepare_copyright_df_for_database(copyright_df, plaintiff_id=None):
    """
    Prepares copyright DataFrame for database upload by:
    1. Splitting "Registration Number / Date" into registration_number and registration_date
    2. Adding plaintiff_ids column
    3. Adding TRO column (always True)
    4. Adding google_status column (NULL by default)

    Args:
        copyright_df: DataFrame with copyright data from UCO
        plaintiff_id: ID of the plaintiff (optional, defaults to empty list)

    Returns:
        DataFrame prepared for database upload
    """
    if copyright_df is None or copyright_df.empty:
        return pd.DataFrame()

    df_prepared = copyright_df.copy()

    # Ensure registration_number and registration_date columns exist and have correct types.
    # These columns are expected to be created by the format_record function before this stage.
    if "registration_number" not in df_prepared.columns:
        df_prepared["registration_number"] = "" 
    df_prepared["registration_number"] = df_prepared["registration_number"].astype(str)

    if "registration_date" not in df_prepared.columns:
        df_prepared["registration_date"] = pd.NaT 
    # Ensure registration_date is datetime, converting if it's a string.
    df_prepared["registration_date"] = pd.to_datetime(df_prepared["registration_date"], errors='coerce')

    # Add plaintiff_ids column (as JSON string for MySQL)
    if plaintiff_id is not None:
        df_prepared["plaintiff_ids"] = json.dumps([int(plaintiff_id)])
    else:
        df_prepared["plaintiff_ids"] = json.dumps([])

    # Add TRO column (always True)
    df_prepared["TRO"] = True

    # Add google_status column (NULL by default)
    df_prepared["google_status"] = None

    return df_prepared


def save_copyright_to_database(copyright_df, plaintiff_id=None):
    """
    Saves copyright data to the database.

    Args:
        copyright_df: DataFrame with copyright data from UCO
        plaintiff_id: ID of the plaintiff (optional)

    Returns:
        pd.DataFrame or None: The prepared DataFrame if save was successful and data is present, else None.
    """
    try:
        if copyright_df is None or copyright_df.empty:
            log_message("No copyright data to save to database", level="INFO")
            return None

        # Prepare the DataFrame for database upload
        df_prepared = prepare_copyright_df_for_database(copyright_df, plaintiff_id)

        if df_prepared.empty:
            log_message("No copyright data after preparation", level="WARNING")
            return None

        # Save to database using the existing function
        insert_and_update_df_to_GZ_batch(df_prepared, "tb_copyright", "registration_number")

        log_message(f"Successfully saved {len(df_prepared)} copyright records to database", level="INFO")
        return df_prepared

    except Exception as e:
        log_message(f"Error saving copyright data to database: {str(e)}", level="ERROR")
        return None


def update_copyright_google_status(registration_number, status):
    """
    Updates the google_status for a copyright record in the database.

    Args:
        registration_number: Copyright registration number
        status: Status to set ('no_image_found' or 'image_found')

    Returns:
        bool: True if update was successful
    """
    try:
        if status not in ['no_image_found', 'image_found']:
            log_message(f"Invalid google_status: {status}", level="ERROR")
            return False

        conn = get_gz_connection()
        if conn is None:
            log_message("Failed to get database connection", level="ERROR")
            return False

        with conn.cursor() as cursor:
            cursor.execute(
                "UPDATE tb_copyright SET google_status = %s WHERE registration_number = %s",
                (status, registration_number)
            )
            conn.commit()

        conn.close()
        log_message(f"Updated google_status for copyright {registration_number} to {status}", level="INFO")
        return True

    except Exception as e:
        log_message(f"Error updating google_status for copyright {registration_number}: {str(e)}", level="ERROR")
        return False


# def get_copyright_from_database_by_reg_no(registration_number: str) -> Optional[pd.DataFrame]:
#     """
#     Retrieves copyright information from the database for a given registration number
#     using get_table_from_GZ.

#     Args:
#         registration_number: The copyright registration number.

#     Returns:
#         A DataFrame containing the copyright information if found, else None.
#         The DataFrame will have expected columns like: 'registration_number', 'title',
#         'copyright_claimant', 'names', 'registration_date', 'plaintiff_ids', 'type_of_work', 'basis_of_claim'.
#     """
#     from DatabaseManagement.ImportExport import get_table_from_GZ # Import here to avoid potential circular imports at module level
#     # Basic check for registration_number. Consider more robust sanitization if sources are untrusted.
#     # Allow alphanumeric, hyphens, and spaces, as these are common in registration numbers.
#     if not registration_number or not all(c.isalnum() or c in ['-', ' ', '(', ')'] for c in registration_number): # Adjusted to be more permissive for valid chars
#         log_message(f"Potentially invalid registration number format for DB query: {registration_number}", level="WARNING")
#         # Allow processing to continue but log it. Strict validation might be too restrictive.

#     # Escape single quotes in registration_number to prevent SQL errors/injection.
#     safe_reg_no = registration_number.replace("'", "''")
#     where_clause = f"registration_number = '{safe_reg_no}'"
    
#     try:
#         df = get_table_from_GZ(table_name="tb_copyright", where_clause=where_clause)
#         if df is not None and not df.empty:
#             if 'registration_date' in df.columns:
#                 df['registration_date'] = pd.to_datetime(df['registration_date'], errors='coerce')
            
#             expected_columns = ['registration_number', 'title', 'copyright_claimant', 
#                                 'registration_date', 'plaintiff_ids', 'type_of_work', 'basis_of_claim', 'names']
#             columns_to_return = [col for col in expected_columns if col in df.columns]
#             return df[columns_to_return].copy()
#         return None
#     except Exception as e:
#         log_message(f"Error fetching copyright {registration_number} from database using get_table_from_GZ: {e}", level="ERROR")
#         return None


# def get_copyrights_from_database_by_plaintiff_id(plaintiff_id: int) -> pd.DataFrame:
#     """
#     Retrieves all copyright records associated with a given plaintiff ID from the database.
#     Uses get_table_from_GZ.

#     Args:
#         plaintiff_id: The ID of the plaintiff.

#     Returns:
#         A DataFrame containing copyright information if found, else an empty DataFrame.
#         Columns are similar to get_copyright_from_database_by_reg_no.
#     """
#     from DatabaseManagement.ImportExport import get_table_from_GZ # Import here
#     # plaintiff_id is an int. CAST({plaintiff_id} AS JSON) is appropriate for the SQL query.
#     where_clause = f"JSON_CONTAINS(plaintiff_ids, CAST({plaintiff_id} AS JSON))"
    
#     try:
#         df = get_table_from_GZ(table_name="tb_copyright", where_clause=where_clause, force_refresh=False) # Use cache
#         if not df.empty:
#             df['registration_date'] = pd.to_datetime(df['registration_date'], errors='coerce')
#             expected_columns = ['registration_number', 'title', 'copyright_claimant', 
#                                 'registration_date', 'plaintiff_ids', 'type_of_work', 'basis_of_claim', 'names']
#             columns_to_return = [col for col in expected_columns if col in df.columns]
#             return df[columns_to_return].copy()
#         return pd.DataFrame() # Return empty DataFrame if nothing found
#     except Exception as e:
#         log_message(f"Error fetching copyrights for plaintiff ID {plaintiff_id} from database: {e}", level="ERROR")
#         return pd.DataFrame() # Return empty DataFrame on error
