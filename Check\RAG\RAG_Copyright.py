import os
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
from langfuse.decorators import observe, langfuse_context

# Function to find the most similar image
@observe()
def find_most_similar_copyrights(query_image_paths, top_n=3, similarity_threshold=0.75):
    """
    Find the most similar image(s) to the query image in the embeddings database.
    
    Args:
        query_image_paths (list[str]): Paths to the query images.
        top_n (int): Number of top similar images to return.
        similarity_threshold (float): Minimum cosine similarity score to consider a match.
        
    Returns:
        List[Tuple[str, float]]: List of (image_id, similarity_score) for the top matches.
    """

    if len(query_image_paths) == 0:
        return []
    
    # Extract embeddings from the structured array
    from Check.RAG.RAG_Inference import structured_copyright_array  # Why here? only after get_efficientnet_embeddings are we sure that it has been populated
    ip_embeddings_siglip = np.array([entry['embedding_siglip'] for entry in structured_copyright_array])
    
    # Siglip: create embeddings for query image
    from Check.RAG.RAG_Inference import get_siglip_embeddings # why here? So that if other part of the code already loaded the models, we use it.If we import at the top, it will import before the model is loaded, so in that import, the model is not loaded.
    query_embeddings_siglip = get_siglip_embeddings(query_image_paths, data_type="image")

    # Compute cosine similarity between all query embeddings and all stored embeddings
    # Siglip embeddings are typically pre-normalized, so cosine similarity is directly comparable.
    # Scores range from -1 to 1. Higher is more similar.
    # We can map to [0,1] by (score + 1) / 2 if needed, or use raw scores.
    # For now, using raw cosine similarity scores.
    similarities_siglip = cosine_similarity(query_embeddings_siglip, ip_embeddings_siglip)

    all_matches = []
    seen_filenames = set()

    for i in range(len(query_image_paths)):
        current_similarity_scores = similarities_siglip[i]
        if len(current_similarity_scores) == 0:
            continue

        # Get top_n matches based on similarity_threshold
        sorted_indices = np.argsort(current_similarity_scores)[::-1]
        
        eligible_indices = [idx for idx in sorted_indices if current_similarity_scores[idx] >= 0.75][:top_n]

        for idx in eligible_indices:
            entry = structured_copyright_array[idx]
            filename = entry['filename']
            current_score = float(current_similarity_scores[idx])

            # Check if this filename (from DB) has already been added for this query image or any other query image in the batch
            # If we want top_n unique DB items across all query images, this logic needs adjustment.
            # Current logic: top_n for *each* query image.
            if filename in seen_filenames: # Simple check to avoid duplicate DB items in the final list if top_n is high
                # Check if this new score for the same DB item is higher
                existing_match = next((m for m in all_matches if m['filename'] == filename), None)
                if existing_match and current_score > float(existing_match['similarity']):
                    existing_match['similarity'] = f"{current_score:.2f}"
                    existing_match['query_image_path'] = query_image_paths[i] # Update query path if it's a better match for this one
                continue

            match_info = {
                'query_image_path': query_image_paths[i],
                'filename': filename,
                'full_filename': entry['full_filename'],
                'reg_no': entry['reg_no'],
                'similarity': f"{current_score:.2f}",
                'plaintiff_name': entry['plaintiff_name'],
                'docket': entry['docket'],
                'number_of_cases': entry['number_of_cases'],
                'approach': 'siglip'
            }
            all_matches.append(match_info)
            seen_filenames.add(filename)

    # Sort all matches by similarity
    all_matches.sort(key=lambda x: float(x['similarity']), reverse=True)
    langfuse_context.update_current_observation(metadata=all_matches)

    # Return up to top_n overall best matches if that's desired, based on score
    # This is a bad idea because often the Top1 of a picture is lower score than the top3 of another picture, but a better results
    # final_matches = all_matches[:top_n] # If top_n is overall

    return all_matches


if __name__ == "__main__":
    pass
    # build_embeddings_dataset()
    # query_image_path = "D:/Win10User/Downloads/68_A_Stanislav Yurievich Osipov.jpg"
    # query_image_path = "D:/Win10User/Downloads/1_A_GOPRO_cropped.jpg"
    # results = find_most_similar_copyright(query_image_path, top_n=3, similarity_threshold=0.4)

    # if results:
    #     print("Top matches:")
    #     for match in results:
    #         print(f"Image: {match['filename']}")
    #         print(f"Similarity: {match['similarity']:.2f}")
    #         print(f"Plaintiff: {match['plaintiff_name']}")
    #         print(f"Docket: {match['docket']}")
    #         print(f"Total Cases: {match['number_of_cases']}")
    #         print("-" * 50)
    # else:
    #     print("No similar images found above the threshold.")