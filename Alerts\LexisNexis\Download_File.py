from langfuse.decorators import observe
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import NoSuchElementException, StaleElementReferenceException, TimeoutException
from selenium.webdriver.remote.webelement import WebElement # Add this import
from typing import Optional, List # Add typing imports
from datetime import date # Add date import
import time
import os
import re # Import re for regex in renaming
from Alerts.Chrome_Driver import set_download_directory, move_mouse_to, random_delay
from Common.Constants import local_case_folder
from Common.Constants import sanitize_name
# from FileManagement.FileNames import handle_duplicate_files_on_nas, handle_duplicate_files
import pandas as pd
from logdata import log_message
import traceback # Import traceback for detailed error logging
from langfuse.decorators import langfuse_context


@observe(capture_input=False, capture_output=False)
def get_step_details(driver, step_row_element: WebElement) -> List[dict]:
    """
    Clicks the step link, extracts attachment details from the modal using a context manager, and closes it.
    """
    attachment_details = []

    try:
        # Open modal:
        download_cell = WebDriverWait(step_row_element, 10).until(lambda el: el.find_elements(By.TAG_NAME, 'td')[1])
        link_element = WebDriverWait(download_cell, 10).until(EC.element_to_be_clickable((By.TAG_NAME, 'a')))
        move_mouse_to(driver, link_element)
        link_element.click()
        # download_modal = WebDriverWait(driver, 20).until(EC.presence_of_element_located((By.CLASS_NAME, "proceeding-container"))) # Does not include the buttons
        download_modal = WebDriverWait(driver, 20).until(EC.presence_of_element_located((By.CSS_SELECTOR, "div.ladialog.large")))


        # Actions within the successfully opened modal
        attachment_containers = download_modal.find_elements(By.CSS_SELECTOR, "div.attachment-container")
        log_message(f"        Found {len(attachment_containers)} attachment containers in modal:", level='DEBUG')

        for i, container in enumerate(attachment_containers):
            try:
                # Attempt to find the label containing the attachment name
                name_element = container.find_element(By.CSS_SELECTOR, "label") # Common pattern
                attachment_name = name_element.text.strip() if name_element else f"Attachment_{i+1}"
                attachment_details.append({'index': i + 1, 'name': attachment_name, 'processed_already': False})
                log_message(f"          - Extracted index: {i + 1}, Extracted name: {attachment_name}", level='DEBUG')
            except NoSuchElementException:
                    log_message(f"Could not find name element for attachment {i+1}. Using default name.", level='WARNING')
                    attachment_details.append({'index': i + 1, 'name': f"Attachment_{i+1}_NameNotFound", 'processed_already': False})
            # Keep inner exception handling for individual item robustness
            except Exception as e_inner:
                    log_message(f"Error extracting details for attachment {i+1}: {e_inner}", level='ERROR')
                    attachment_details.append({'index': i + 1, 'name': f"Attachment_{i+1}_Error", 'processed_already': False})

        # Close Modal by clicking the cancel button
        cancel_button = download_modal.find_element(By.CSS_SELECTOR, "button.button.secondary[type='submit']")
        move_mouse_to(driver, cancel_button)
        cancel_button.click()

    except (NoSuchElementException, StaleElementReferenceException) as e:
            log_message(f"Error finding attachment containers within modal: {e}", level='WARNING')
    except Exception as e:
            log_message(f"Unexpected error extracting details within modal: {e}\n{traceback.format_exc()}", level='ERROR')

    return attachment_details


@observe(capture_input=False, capture_output=False)
def get_step_file(driver, row: WebElement, docket_number: str, court: str, case_date_filed: date, step_nb: str, doc_date_filed: date, options: dict, target_attachment_indices: Optional[List[int]] = None):
    """
    Downloads files for a specific case step, checking local cache first, based on options.
    Uses context managers for modal interactions. Preserves original cache logic.

    Returns:
        A tuple: (list of successfully downloaded/retrieved file paths, int pacer_retrievals_count, int downloaded_files_count)
        pacer_retrievals_count is the number of files that had 'retrieving' in their status text.
        downloaded_files_count is the number of files newly downloaded and successfully saved in this call.
    """
    court_abbrev = get_court_abbreviation(court)
    normalized_docket = sanitize_name(docket_number)
    date_filed_str = pd.to_datetime(doc_date_filed).strftime('%Y-%m-%d') if doc_date_filed else "UnknownDate"
    step_nb_str = str(step_nb)
    download_mode = options.get('download_mode', 'all')
    download_directory = os.path.join(local_case_folder, sanitize_name(f"{case_date_filed.strftime('%Y-%m-%d')} - {docket_number}"), step_nb_str.split(".")[0]) # convert "1.00" to 1.00 to 1 to "1"
    renamed_file_paths = []
    existing_file_paths = []
    pacer_retrievals_count = 0  # Track files retrieved from PACER
    downloaded_files_count = 0

    # log_message(f"Step {step_nb_str}: Proceeding with browser download setup.")
    os.makedirs(download_directory, exist_ok=True)
    set_download_directory(driver, download_directory)
    existing_local_pdf_files = [f for f in os.listdir(download_directory) if f.lower().endswith('.pdf') ]

    # --- First Modal: File Selection ---
    checkboxes_to_select_elements = [] # Store checkbox elements to click
    docs_to_download_names = [] # Store labels of docs to download (for logging)
    view_files_downloaded_count = 0

    try:
        # Open modal:
        download_cell = WebDriverWait(row, 5).until(lambda el: el.find_elements(By.TAG_NAME, 'td')[1])
        link_element = WebDriverWait(download_cell, 5).until(EC.element_to_be_clickable((By.TAG_NAME, 'a')))
        move_mouse_to(driver, link_element)
        link_element.click()
        random_delay()
        # download_modal = WebDriverWait(driver, 20).until(EC.presence_of_element_located((By.CLASS_NAME, "proceeding-container"))) # Does not include the buttons
        selection_modal = WebDriverWait(driver, 20).until(EC.presence_of_element_located((By.CSS_SELECTOR, "div.ladialog.large")))


        # --- Start: Cache-Aware Download Logic (Original Comparison Preserved) ---
        log_message(f"           Processing selection modal. Mode: '{download_mode}', Target Indices: {target_attachment_indices}, Local PDFs found: {len(existing_local_pdf_files)}")

        all_checkboxes_in_modal = []

        # 1. Get Main Document Details and Determine if to select the checkbox
        main_label_element = selection_modal.find_element(By.CSS_SELECTOR, "div.doc-title label")
        main_checkbox_element = selection_modal.find_element(By.CSS_SELECTOR, "div.doc-title input[type='checkbox'][name='selecteddocs']")

        # Add a wait condition for the text to be present
        try:
            WebDriverWait(selection_modal, 10).until(lambda driver: main_label_element.text.strip() != "")
        except TimeoutException:
            log_message(f"           Warning: Timeout waiting for main document label text to load.", level='WARNING')

        main_label_text = main_label_element.text.strip() if main_label_element else "MainDocument"
        standardized_ln_name, expected_file_name = standardize_ln_label(main_label_text, court_abbrev, normalized_docket, date_filed_str, step_nb_str, doc_nb = 0) # Standardize for potential use in renaming later
        all_checkboxes_in_modal.append(main_checkbox_element)
        if download_mode == 'main_only' or download_mode == 'all':
            log_message(f"           Found main document: '{main_label_text}' -> standardized: '{standardized_ln_name}'", level='DEBUG')
            if expected_file_name in existing_local_pdf_files:
                log_message(f"           Document '{expected_file_name}' found in local folder. Skipping download.", level='INFO')
                existing_file_paths.append(os.path.join(download_directory, expected_file_name))
            else: # If not local, mark for download
                checkboxes_to_select_elements.append(main_checkbox_element) # Store the element
                docs_to_download_names.append(main_label_text) # Store the name for logging
                log_message(f"           📥 Marked for download: '{standardized_ln_name}' (Expected File Name: '{expected_file_name}').", level='INFO')


        # 2. Get Attachment Details and Determine Checkboxes to Select
        attach_checkbox_elements = selection_modal.find_elements(By.CSS_SELECTOR, "div.attachment-container input[type='checkbox'][name='selectedattachments']")
        all_checkboxes_in_modal.extend(attach_checkbox_elements) # Add all found checkboxes to the list
        # log_message(f"           Found {len(attach_checkbox_elements)} attachments.", level='DEBUG')
        attach_label_elements = selection_modal.find_elements(By.CSS_SELECTOR, "div.attachment-container label")
        for i, (label, checkbox) in enumerate(zip(attach_label_elements, attach_checkbox_elements)):
            attach_label_text = label.text.strip() if label else f"Attachment_{i+1}"
            standardized_ln_name, expected_file_name = standardize_ln_label(attach_label_text, court_abbrev, normalized_docket, date_filed_str, step_nb_str, doc_nb = i+1) # Standardize for potential use in renaming later
            if (download_mode == 'all' and target_attachment_indices is None) or (target_attachment_indices is not None and (i + 1) in target_attachment_indices):
                # log_message(f"           Found attachment {i+1}: '{attach_label_text}' -> standardized: '{standardized_ln_name}'", level='DEBUG')
                # if expected_file_name in existing_local_pdf_files:
                if any(str(i)+"_"+standardized_ln_name+".pdf" in existing_local_pdf_file for existing_local_pdf_file in existing_local_pdf_files): # This will capture Maidalv and LexisNexis file names by testing: '1_Exhibit_Schedule_A_Redacted_.pdf' in any of the files in folder   
                    log_message(f"              Document '{expected_file_name}' found in local folder. Skipping download.", level='INFO')
                    existing_file_paths.append(os.path.join(download_directory, expected_file_name))
                else:
                    checkboxes_to_select_elements.append(checkbox)
                    docs_to_download_names.append(attach_label_text) # Store the name for logging
                    log_message(f"              📥 Marked for download: '{standardized_ln_name}' (Expected File Name: '{expected_file_name}').", level='INFO')

        # 3. Modify Checkbox States (if any need downloading)
        if len(checkboxes_to_select_elements) == 0:
            log_message(f"           No documents need to be selected (either due to cache or selection mode).", level='INFO')
            cancel_button = selection_modal.find_element(By.CSS_SELECTOR, "button.button.secondary[type='submit']")
            move_mouse_to(driver, cancel_button)
            cancel_button.click()
        elif len(checkboxes_to_select_elements) > 0:
            log_message(f"           Deselecting all {len(all_checkboxes_in_modal)} checkboxes first, then selecting {len(checkboxes_to_select_elements)} required checkboxes for download: {docs_to_download_names}", level='INFO')
            # Deselect all first using the "Deselect All" button
            try:
                deselect_all_button = WebDriverWait(selection_modal, 5).until(EC.element_to_be_clickable((By.CSS_SELECTOR, "button.deselectAll.button.secondary[type='button']")))
                deselect_all_button.click()
                log_message(f"           Clicked 'Deselect All' button.", level='INFO')
                random_delay() # Add a small delay after clicking
            except TimeoutException:
                log_message(f"           'Deselect All' button not found or not clickable. Proceeding with individual deselection.", level='WARNING')
                # Fallback to individual deselection if button not found
                for checkbox in all_checkboxes_in_modal:
                    if checkbox.is_displayed() and checkbox.is_selected():
                        checkbox.click() # Direct click
            
            # Select required ones
            for checkbox in checkboxes_to_select_elements:
                if checkbox.is_displayed() and not checkbox.is_selected():
                    checkbox.click()
            # Fix lexis bug: untick 1st exhibit checkbox
            if len(all_checkboxes_in_modal) > 1 and all_checkboxes_in_modal[1].is_selected() and all_checkboxes_in_modal[1] not in checkboxes_to_select_elements:
                random_delay(0.25, 0.5)
                all_checkboxes_in_modal[1].click()

            # 4. If multiple tickbox were selected: until the "combine documents" if present (inside the action that opens the modal)
            if len(checkboxes_to_select_elements) > 1: # i.e. mode is "all"
                combine_checkbox = WebDriverWait(driver, 3).until(EC.presence_of_element_located((By.NAME, 'combineattachments')))
                if combine_checkbox and combine_checkbox.is_selected():
                    combine_checkbox.click()
                    random_delay()
                    # log_message(f"           Unticked 'combine attachments' checkbox.", level='DEBUG')

            # 5. Click "Get Documents"
            get_documents_button = selection_modal.find_element(By.CSS_SELECTOR, "button.button.primary[type='submit']")
            move_mouse_to(driver, get_documents_button)
            get_documents_button.click()

        # --- End: Cache-Aware Download Logic ---

    except (NoSuchElementException, StaleElementReferenceException) as e:
            log_message(f"           ❌ Error processing elements within selection modal: {e}", level='WARNING')
            # Modal will be closed by context manager
    except Exception as e:
            log_message(f"           ❌ Unexpected error processing selection modal: {e}\n{traceback.format_exc()}", level='ERROR')
            # Modal will be closed by context manager

    # Now we are ready to download the file. We proceed only if the first modal was processed and files need downloading ---
    if len(docs_to_download_names) > 0:
        log_message(f"           Proceeding to download {len(docs_to_download_names)} selected files.")

        # --- Second Modal: Download Status ---
        try:
            log_message(f"           'Get Documents' was clicked (by first modal's context manager). Waiting for status modal...", level='DEBUG')
            WebDriverWait(driver, 20).until(EC.presence_of_element_located((By.ID, 'viewfile')))
            # status_modal = WebDriverWait(driver, 20).until(EC.presence_of_element_located((By.CLASS_NAME, "status-table"))) # Does not include the buttons
            status_modal = WebDriverWait(driver, 20).until(EC.presence_of_element_located((By.CSS_SELECTOR, "div.ladialog.large")))

            # log_message(f"           Download status modal appeared.", level='DEBUG')
            view_files = status_modal.find_elements(By.ID, 'viewfile')
            # log_message(f"           Modal shows {len(view_files)} files potentially downloading, of which we are getting only {len(docs_to_download_names)}.")

            if len(view_files) == 0 and len(docs_to_download_names) > 0:
                log_message(f"           ⚠️ get_step_file: WARNING - Expected {len(docs_to_download_names)} files but status modal shows 0.", level='WARNING')

            view_files_processed_flags = [False] * len(view_files)
            pacer_retrieval_counted_flags = [False] * len(view_files) # Track if PACER retrieval has been counted for this file
            start_time = time.time()
            # Adjust timeout based on expected files, not just those initially visible
            timeout_seconds = max(180, len(docs_to_download_names) * 40)

            # --- Download Monitoring Loop (inside status modal context) ---
            while sum(view_files_processed_flags) < len(docs_to_download_names) and (time.time() - start_time < timeout_seconds):
                processed_in_loop = False
                try:
                    # Re-find elements within the *current* status_modal context
                    current_view_files = status_modal.find_elements(By.ID, 'viewfile')
                    current_statuses = status_modal.find_elements(By.ID, 'status')

                    for i in range(len(view_files)):
                        if current_statuses[i].text != "Not Selected" and not view_files_processed_flags[i]:
                            status_text = current_statuses[i].text.lower() if i < len(current_statuses) else ""
                            view_file_text = current_view_files[i].text.lower() if i < len(current_view_files) else ""

                            if 'view' in view_file_text:
                                try:
                                    view_link = current_view_files[i].find_element(By.TAG_NAME, 'a')
                                    if view_link.is_displayed() and view_link.is_enabled():
                                        move_mouse_to(driver, view_link)
                                        view_link.click() # Auto download
                                        random_delay(0.6, 1.2)
                                        view_files_processed_flags[i] = True
                                        view_files_downloaded_count += 1
                                        processed_in_loop = True
                                        log_message(f"           Initiated download for file {view_files_downloaded_count}/{len(docs_to_download_names)}")
                                    # else: Link not ready yet

                                except StaleElementReferenceException:
                                    log_message(f"           ❌ Stale element clicking view link for file {i+1}. Will retry loop.", level='WARNING')
                                    break # Break inner loop to refresh elements
                                except Exception as click_err:
                                    log_message(f"           ❌ Error clicking view link for file {i+1}: {click_err}", level='ERROR')
                                    view_files_processed_flags[i] = True # Mark as processed (failed)
                                    processed_in_loop = True

                            elif 'failed' in status_text:
                                log_message(f"           File {i+1} failed to download per status.")
                                view_files_processed_flags[i] = True
                                processed_in_loop = True
                            elif 'retrieving' in status_text:
                                # This is a PACER retrieval
                                if not pacer_retrieval_counted_flags[i]: # Count only once per file
                                    pacer_retrievals_count += 1
                                    pacer_retrieval_counted_flags[i] = True
                                pass # Still waiting
                            elif status_text == "":
                                pass # Still waiting
                            else: # Unexpected status
                                log_message(f"           File {i+1} has unexpected status '{status_text}'. Marking processed.", level='WARNING')
                                view_files_processed_flags[i] = True
                                processed_in_loop = True

                    if processed_in_loop:
                        start_time = time.time() # Reset timer on activity

                except StaleElementReferenceException:
                    log_message(f"           ❌ Stale element reference during download status check. Refreshing elements.", level='WARNING')
                    time.sleep(1)
                    # Continue loop, hoping elements refresh on next iteration
                    continue
                except Exception as loop_err:
                    log_message(f"           ❌ Error in download loop: {loop_err}", level='ERROR')
                    time.sleep(1) # Pause before next loop iteration

                if sum(view_files_processed_flags) < len(docs_to_download_names):
                    time.sleep(1.5) # Wait before checking statuses again

            # Close Status modal by clicking "Close"
            close_button = status_modal.find_element(By.CSS_SELECTOR, "button.button.primary[type='submit']")
            move_mouse_to(driver, close_button)
            close_button.click()

            # --- End Download Monitoring Loop ---

            if sum(view_files_processed_flags) < len(docs_to_download_names):
                log_message(f"           Warning: Download loop timed out or finished incompletely for step {step_nb_str}. Processed: {sum(view_files_processed_flags)}/{len(view_files)}")

            log_message(f"           Browser download process finished. Initiated {view_files_downloaded_count} downloads. Processed {sum(view_files_processed_flags)} statuses in modal. Renaming files...")
            renamed_file_paths = rename_downloaded_files(download_directory, court_abbrev, normalized_docket, step_nb_str.split(".")[0], date_filed_str)
            downloaded_files_count = len(renamed_file_paths)

        except (NoSuchElementException, StaleElementReferenceException) as e:
                log_message(f"           ❌ Error processing elements within status modal for step {step_nb_str}: {e}", level='WARNING')
                # Modal will be closed by context manager
        except Exception as e:
                log_message(f"           ❌ Unexpected error processing status modal: {e}\n{traceback.format_exc()}", level='ERROR')
                # Modal will be closed by context manager

    elif not checkboxes_to_select_elements:
        log_message(f"           No files needed downloading based on cache/mode. Skipping download modal.")
        
    log_message(f"           Step {step_nb_str}: Newly downloaded: {downloaded_files_count}, Cached: {len(existing_file_paths)}. PACER retrievals: {pacer_retrievals_count}", level='INFO')
    langfuse_context.update_current_observation(
        input={
            "StepNumber": step_nb_str,
            "DownloadMode": download_mode,
            "NumFilesToDownload": len(checkboxes_to_select_elements),
            "NumFilesAlreadyLocal": len(existing_file_paths)
        },
        output={
            "NumFilesDownloaded": downloaded_files_count,
            "NumFilesAlreadyLocal": len(existing_file_paths),
            "PACERRetrievals": pacer_retrievals_count
        }
    )
            
    return existing_file_paths + renamed_file_paths, pacer_retrievals_count, downloaded_files_count



# Helper function for LN label standardization
def standardize_ln_label(label_text, court_abbrev, normalized_docket, date_filed_str, step_nb_str, doc_nb):
    if not label_text:
        return "",""
    # Replace non-alphanumeric with underscore
    s = re.sub(r'[^a-zA-Z0-9]+', '_', label_text.strip())
    # Collapse multiple underscores to one
    s = re.sub(r'_+', '_', s)
    # Truncate
    s = s[:50]

    if doc_nb == 0: # Main document
        suffix_from_std = f"{s}.pdf" # Suffix based on standardized modal label
    else:
        suffix_from_std = f" {str(doc_nb-1)}_{s}.pdf" # Suffix based on standardized modal label

    expected_file_name = f"{court_abbrev}_DC_{normalized_docket}_{date_filed_str}_{step_nb_str.split(".")[0]}_{suffix_from_std}"

    return s, expected_file_name

def get_court_abbreviation(court):
    # Split the court name and take first letter of each word, excluding "District" and "Court"
    excluded_words = ['district', 'court']
    return ''.join(word[0].upper() for word in court.split() if word.lower() not in excluded_words)


def rename_downloaded_files(download_directory, court_abbrev, normalized_docket, step_nb, date_filed_str):
    failed_renames = []
    renamed_file_paths = []

    # --- Phase 1: Wait for all .crdownload files to complete or stabilize ---
    # log_message(f"           Phase 1: Monitoring .crdownload files in {download_directory}", level='INFO')

    # Scan for initial .crdownload files.
    # We list files once at the start of this phase to avoid issues with iterating over a changing directory list during this specific scan.
    try:
        current_files_in_dir = os.listdir(download_directory)
    except FileNotFoundError:
        log_message(f"           ❌ rename_downloaded_files: Download directory {download_directory} not found. Skipping rename process.", level='ERROR')
        return [] # Or handle error as appropriate

    initial_crdownloads = [f for f in current_files_in_dir if f.lower().endswith('.crdownload')]

    if initial_crdownloads:
        # log_message(f"           Found .crdownload files: {initial_crdownloads}. Monitoring each...", level='DEBUG')
        for cr_filename in initial_crdownloads:
            cr_filepath = os.path.join(download_directory, cr_filename)

            # Check if the .crdownload file still exists before starting to monitor it,
            # as it might have been quickly processed by the browser.
            if not os.path.exists(cr_filepath):
                log_message(f"           ❌ rename_downloaded_files:.crdownload file {cr_filename} disappeared before monitoring could start (likely renamed by browser).", level='DEBUG')
                continue

            # log_message(f"           Monitoring {cr_filename} for size stability...", level='DEBUG')

            last_size = -1
            stable_count = 0
            stability_checks_required = 3  # Number of consecutive checks size must be stable
            check_interval = 1  # Seconds between size checks
            file_timeout_seconds = 600  # Max time (seconds) to wait for this one file to stabilize

            file_start_time = time.time()
            monitoring_this_file = True # Flag to control the inner while loop

            while monitoring_this_file and (time.time() - file_start_time < file_timeout_seconds):
                try:
                    if not os.path.exists(cr_filepath):
                        log_message(f"           rename_downloaded_files: {cr_filename} no longer exists (likely because the download completed).", level='INFO')
                        monitoring_this_file = False # Exit while loop
                        break

                    current_size = os.path.getsize(cr_filepath)

                    # Condition for stability: size is the same as last check AND is not zero
                    if current_size == last_size and current_size > 0:
                        stable_count += 1
                    else:
                        stable_count = 0  # Reset if size changes or was/is zero

                    last_size = current_size # Update last_size for the next check

                    if stable_count >= stability_checks_required:
                        # log_message(f"           {cr_filename} size stabilized at {current_size} bytes after {stable_count} checks.", level='INFO')
                        monitoring_this_file = False # Exit while loop
                        break

                    time.sleep(check_interval)
                except FileNotFoundError: # Should be caught by os.path.exists, but as a safeguard
                    log_message(f"           ❌ rename_downloaded_files:{cr_filename} disappeared during monitoring (FileNotFoundError).", level='INFO')
                    monitoring_this_file = False # Exit while loop
                    break
                except Exception as e:
                    log_message(f"           ❌ rename_downloaded_files: Error monitoring {cr_filename}: {e}", level='WARNING')
                    monitoring_this_file = False # Stop monitoring this file on unexpected error
                    break

            if monitoring_this_file: # True if the while loop exited due to timeout
                log_message(f"           ❌ rename_downloaded_files:Timeout waiting for {cr_filename} to stabilize. Last known size: {last_size}", level='WARNING')

        # log_message(f"           Finished monitoring all initial .crdownload files. Pausing briefly for browser finalization...", level='DEBUG')
        time.sleep(3)  # General buffer time for browser to finalize any renames
    else:
        # log_message(f"           No .crdownload files found at the start of renaming process. Pausing briefly.", level='DEBUG')
        time.sleep(1) # Brief pause, similar to the original script's behavior if no .crdownloads were present

    # --- Phase 2: Rename PDF files ---
    # log_message(f"           Phase 2: Processing .pdf files for renaming in {download_directory}", level='INFO')
    # Now, list files again to get the final state after .crdownload processing and the subsequent pause
    try:
        files_for_pdf_processing = os.listdir(download_directory)
    except FileNotFoundError:
        log_message(f"           ❌ rename_downloaded_files: Download directory {download_directory} not found for PDF processing. Aborting.", level='ERROR')
        return []

    for filename in files_for_pdf_processing:
        # .crdownload files should have been handled (either completed and renamed by browser, or timed out) in Phase 1.
        # We are now only interested in .pdf files for renaming.
        if filename.lower().endswith('.pdf') and "-cv-" not in filename.lower():  # if "-cv" is already in the name, it means it is already the correct format
            try:
                # Extract the suffix part (e.g., "1_Exhibit_2.pdf")
                suffix = '_'.join(filename.split("_")[6:])
                new_name = f"{court_abbrev}_DC_{normalized_docket}_{date_filed_str}_{step_nb}_{suffix}"
                old_path = os.path.join(download_directory, filename)
                new_path = os.path.join(download_directory, new_name)

                # Delete existing file if it exists
                if old_path != new_path:
                    if os.path.exists(new_path):
                        os.remove(new_path)

                    os.rename(old_path, new_path)
                    renamed_file_paths.append(new_path)
            except (PermissionError, OSError) as e:
                failed_renames.append((filename, str(e)))

    # Retry failed renames once
    if failed_renames:
        time.sleep(2)  # Wait a bit longer before retry
        for filename, error in failed_renames:
            try:
                if os.path.exists(os.path.join(download_directory, filename)):  # Check if original file still exists
                    suffix = '_'.join(filename.split("_")[6:])
                    new_name = f"{court_abbrev}_DC_{normalized_docket}_{date_filed_str}_{step_nb}_{suffix}"
                    old_path = os.path.join(download_directory, filename)
                    new_path = os.path.join(download_directory, new_name)

                    if old_path != new_path:
                        if os.path.exists(new_path):
                            os.remove(new_path)
                        os.rename(old_path, new_path)
                        renamed_file_paths.append(new_path)
            except (PermissionError, OSError) as e:
                print(f"Failed to rename {filename} after retry: {str(e)}")

    return renamed_file_paths
