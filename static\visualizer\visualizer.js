document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const applyFiltersBtn = document.getElementById('apply-filters');
    const refreshDataBtn = document.getElementById('refresh-data');
    const casesContainer = document.getElementById('cases-container');
    const totalResultsEl = document.getElementById('total-results');
    const lastRefreshEl = document.getElementById('last-refresh');
    const prevPageBtn = document.getElementById('prev-page');
    const nextPageBtn = document.getElementById('next-page');
    const pageInfoEl = document.getElementById('page-info');
    const caseTypeCheckboxes = document.querySelectorAll('input[name="case_type"]');
    const caseTypeDropdownHeader = document.getElementById('case-type-dropdown-header');
    const caseTypeDropdownContent = document.getElementById('case-type-dropdown-content');

    // State
    let currentPage = 1;
    let totalPages = 1;
    let currentData = [];
    let currentFilters = {
        case_number: '',
        plaintiff_name: '',
        plaintiff_id: '',
        case_type: [],
        picture_type: '',
        validation_status: '',
        ip_source: '',
        date_from: '',
        date_to: '',
        sort_by: 'date_filed',
        sort_order: 'desc',
        limit: 20,
        offset: 0
    };

    // Global variable to store proposed plaintiff names
    let proposedPlaintiffNames = {};

    // Initialize
    initializeDropdowns();

    applyUrlParametersAndFetch(); // Fetch cases after applying URL parameters
    // Fetch proposed plaintiff names at startup
    // This function is used to get cached proposed plaintiff names from the backend
    function fetchProposedPlaintiffNames() {
        // Since there's no direct API endpoint to get all proposed names,
        // we'll use the API response data which already includes this information

        // We'll also set up a listener to update our names cache when cases are fetched
        casesContainer.addEventListener('casesLoaded', function(e) {
            // Check if cached_plaintiff_reviews data was included in the API response
            if (e.detail && e.detail.proposed_names) {
                proposedPlaintiffNames = e.detail.proposed_names;
                console.log('Updated proposed plaintiff names', proposedPlaintiffNames);
            }
        });
    }

    // Call it to set up event listener
    fetchProposedPlaintiffNames();

    // Event Listeners
    applyFiltersBtn.addEventListener('click', function() {
        currentPage = 1;
        updateFilters();
        fetchCases();
    });

    refreshDataBtn.addEventListener('click', function() {
        fetchCases(true);
    });

    prevPageBtn.addEventListener('click', function() {
        if (currentPage > 1) {
            currentPage--;
            currentFilters.offset = (currentPage - 1) * currentFilters.limit;
            fetchCases();
        }
    });

    nextPageBtn.addEventListener('click', function() {
        if (currentPage < totalPages) {
            currentPage++;
            currentFilters.offset = (currentPage - 1) * currentFilters.limit;
            fetchCases();
        }
    });

    // Dropdown functionality
    function initializeDropdowns() {
        // Case type dropdown
        caseTypeDropdownHeader.addEventListener('click', function() {
            caseTypeDropdownContent.classList.toggle('show');
        });

        // Close dropdown when clicking outside
        window.addEventListener('click', function(event) {
            if (!event.target.closest('.dropdown-checkbox')) {
                caseTypeDropdownContent.classList.remove('show');
                updateCaseTypeDropdownHeader();
            }
        });

        // Update header text when checkboxes change
        caseTypeCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateCaseTypeDropdownHeader);
        });
    }

    function updateCaseTypeDropdownHeader() {
        const selectedTypes = Array.from(caseTypeCheckboxes)
            .filter(cb => cb.checked)
            .map(cb => cb.value);

        const headerText = selectedTypes.length > 0 ?
            selectedTypes.join(', ') :
            'Select types';

        const headerSpan = caseTypeDropdownHeader.querySelector('span');
        headerSpan.textContent = headerText;
    }

    function applyUrlParametersAndFetch() {
        const urlParams = new URLSearchParams(window.location.search);
        const urlCaseId = urlParams.get('case_id'); // For specific case ID
        const urlPlaintiffId = urlParams.get('plaintiff_id'); // For specific plaintiff ID

        let filtersAppliedFromUrl = false;

        if (urlCaseId) {
            document.getElementById('case_number').value = urlCaseId; // Populate "Case #" input
            filtersAppliedFromUrl = true;
        }
        if (urlPlaintiffId) {
            document.getElementById('plaintiff_id').value = urlPlaintiffId; // Populate "Plaintiff ID" input
            filtersAppliedFromUrl = true;
        }

        if (filtersAppliedFromUrl) {
            currentPage = 1; // Reset to first page
            updateFilters(); // This will pick up values from input fields
            fetchCases();    // Fetch with the new filters
        } else {
            // Default behavior: fetch cases without URL prefill if no relevant params found
            fetchCases(false);
        }
    }

    // Update case functionality
    function showUpdateCaseOverlay(caseId) {
        // Create overlay
        const overlay = document.createElement('div');
        overlay.className = 'overlay';
        overlay.classList.add('show');

        // Create overlay content
        const content = document.createElement('div');
        content.className = 'overlay-content';

        // Overlay header
        const header = document.createElement('div');
        header.className = 'overlay-header';

        const title = document.createElement('div');
        title.className = 'overlay-title';
        title.textContent = 'Reprocess Case';

        const closeBtn = document.createElement('button');
        closeBtn.className = 'close-overlay';
        closeBtn.innerHTML = '&times;';
        closeBtn.addEventListener('click', function() {
            document.body.removeChild(overlay);
        });

        header.appendChild(title);
        header.appendChild(closeBtn);

        // Overlay body
        const body = document.createElement('div');
        body.className = 'overlay-body';

        // Processing Options Section
        const optionsGroup = document.createElement('div');
        optionsGroup.className = 'option-group';

        const optionsTitle = document.createElement('div');
        optionsTitle.className = 'option-group-title';
        optionsTitle.textContent = 'Processing Options:';
        optionsGroup.appendChild(optionsTitle);

        // Create checkboxes for processing options
        const processingOptions = [
            { name: 'update_steps', label: 'Update Steps', checked: true },
            { name: 'process_pictures', label: 'Process Pictures', checked: true },
            { name: 'upload_files_nas', label: 'Upload Files to NAS', checked: true },
            { name: 'upload_files_cos', label: 'Upload Files to COS', checked: true },
            { name: 'run_plaintiff_overview', label: 'Run Plaintiff Overview', checked: true },
            { name: 'run_summary_translation', label: 'Run Summary Translation', checked: true },
            { name: 'run_step_translation', label: 'Run Step Translation', checked: true },
            { name: 'save_to_db', label: 'Save to Database', checked: true }
        ];

        processingOptions.forEach(option => {
            const checkboxOption = document.createElement('div');
            checkboxOption.className = 'checkbox-option';

            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.name = option.name;
            checkbox.id = option.name;
            checkbox.checked = option.checked;

            const label = document.createElement('label');
            label.htmlFor = option.name;
            label.className = 'option-label';
            label.textContent = option.label;

            checkboxOption.appendChild(checkbox);
            checkboxOption.appendChild(label);
            optionsGroup.appendChild(checkboxOption);
        });

        // Processing Mode Section
        const modeGroup = document.createElement('div');
        modeGroup.className = 'option-group';

        const modeTitle = document.createElement('div');
        modeTitle.className = 'option-group-title';
        modeTitle.textContent = 'Processing Mode:';
        modeGroup.appendChild(modeTitle);

        const modeSelect = document.createElement('select');
        modeSelect.id = 'processing_mode';
        modeSelect.name = 'processing_mode';

        const fullReprocessOption = document.createElement('option');
        fullReprocessOption.value = 'full_reprocess';
        fullReprocessOption.textContent = 'Full Reprocess - Reprocess all steps and clear previous state';
        fullReprocessOption.selected = true;

        const resumeOption = document.createElement('option');
        resumeOption.value = 'resume';
        resumeOption.textContent = 'Resume - Load saved state and only process steps not previously completed';

        modeSelect.appendChild(fullReprocessOption);
        modeSelect.appendChild(resumeOption);

        const modeDescription = document.createElement('div');
        modeDescription.className = 'option-description';
        modeDescription.textContent = 'Resume will process steps not yet processed until all IP is found, while full_reprocess will reprocess the steps already processed and then process steps not yet processed';

        modeGroup.appendChild(modeSelect);
        modeGroup.appendChild(modeDescription);

        // Refresh Days Threshold Section
        const thresholdGroup = document.createElement('div');
        thresholdGroup.className = 'option-group';

        const thresholdTitle = document.createElement('div');
        thresholdTitle.className = 'option-group-title';
        thresholdTitle.textContent = 'Refresh Days Threshold:';
        thresholdGroup.appendChild(thresholdTitle);

        const thresholdInput = document.createElement('input');
        thresholdInput.type = 'number';
        thresholdInput.id = 'refresh_days_threshold';
        thresholdInput.name = 'refresh_days_threshold';
        thresholdInput.value = '15';
        thresholdInput.min = '1';
        thresholdInput.max = '365';

        const thresholdDescription = document.createElement('div');
        thresholdDescription.className = 'option-description';
        thresholdDescription.textContent = 'Number of days to use as threshold for refresh operations';

        thresholdGroup.appendChild(thresholdInput);
        thresholdGroup.appendChild(thresholdDescription);

        // Add groups to body
        body.appendChild(optionsGroup);
        body.appendChild(modeGroup);
        body.appendChild(thresholdGroup);

        // Overlay footer
        const footer = document.createElement('div');
        footer.className = 'overlay-footer';

        const cancelBtn = document.createElement('button');
        cancelBtn.className = 'btn secondary';
        cancelBtn.textContent = 'Cancel';
        cancelBtn.addEventListener('click', function() {
            document.body.removeChild(overlay);
        });

        const proceedBtn = document.createElement('button');
        proceedBtn.className = 'btn primary';
        proceedBtn.textContent = 'Proceed';
        proceedBtn.addEventListener('click', function() {
            // Get processing options from checkboxes
            const processingOptions = {};
            processingOptions.update_steps = document.getElementById('update_steps').checked;
            processingOptions.process_pictures = document.getElementById('process_pictures').checked;
            processingOptions.upload_files_nas = document.getElementById('upload_files_nas').checked;
            processingOptions.upload_files_cos = document.getElementById('upload_files_cos').checked;
            processingOptions.run_plaintiff_overview = document.getElementById('run_plaintiff_overview').checked;
            processingOptions.run_summary_translation = document.getElementById('run_summary_translation').checked;
            processingOptions.run_step_translation = document.getElementById('run_step_translation').checked;
            processingOptions.save_to_db = document.getElementById('save_to_db').checked;

            // Get processing mode
            processingOptions.processing_mode = document.getElementById('processing_mode').value;

            // Get refresh days threshold
            processingOptions.refresh_days_threshold = parseInt(document.getElementById('refresh_days_threshold').value);

            // Remove overlay
            document.body.removeChild(overlay);

            // Call API to update case with new options structure
            updateCaseWithOptions(caseId, processingOptions);
        });

        footer.appendChild(cancelBtn);
        footer.appendChild(proceedBtn);

        // Assemble overlay
        content.appendChild(header);
        content.appendChild(body);
        content.appendChild(footer);
        overlay.appendChild(content);
        document.body.appendChild(overlay);
    }

    function updateCaseWithOptions(caseId, processingOptions) {
        // Find the case card
        const caseCard = document.querySelector(`.case-card .update-case-btn[data-case-id="${caseId}"]`).closest('.case-card');

        // Create progress log
        const progressLog = document.createElement('div');
        progressLog.className = 'log-section';
        // Add a unique ID to the progress log for easier selection
        const progressLogId = `reprocess-log-${caseId}`;
        progressLog.id = progressLogId;


        const logHeader = document.createElement('div');
        logHeader.className = 'log-section-header';

        const logTitle = document.createElement('div');
        logTitle.textContent = 'Reprocess Progress';

        const closeLogBtn = document.createElement('button');
        closeLogBtn.className = 'close-log';
        closeLogBtn.innerHTML = '&times;';
        closeLogBtn.addEventListener('click', function() {
            progressLog.remove();
        });

        logHeader.appendChild(logTitle);
        logHeader.appendChild(closeLogBtn);

        const logContent = document.createElement('div');
        logContent.className = 'log-section-content';
        logContent.textContent = 'Starting reprocess...\n';

        progressLog.appendChild(logHeader);
        progressLog.appendChild(logContent);

        // Add it after case-details-row
        const detailsRow = caseCard.querySelector('.case-details-row');
        if (detailsRow && detailsRow.nextSibling) {
            caseCard.insertBefore(progressLog, detailsRow.nextSibling);
        } else {
            caseCard.appendChild(progressLog);
        }

        // Prepare request data with new structure
        const requestData = {
            case_id: caseId,
            processing_options: processingOptions
        };

        // Call API
        fetch('/api/cases/reprocess', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData),
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.queued) {
                logContent.textContent += 'Case is already queued for reprocessing.\n';
            } else if (data.queue_position > 1) {
                logContent.textContent += `Case added to queue. Position: ${data.queue_position}\n`;
                logContent.textContent += 'The case will be processed when previous cases are complete.\n';
            } else {
                logContent.textContent += 'Reprocess initiated successfully. The case will be reprocessed in the background.\n';
            }

            // Setup EventSource for streaming updates
            const eventSource = new EventSource(`/api/cases/reprocess/status/${caseId}`);

            eventSource.onmessage = function(event) {
                const message = event.data;
                // Check if the message contains queue information
                if (message.includes('Queue position:') || message.includes('queue position:') ||
                    message.includes('Case added to queue') || message.includes('Starting next case in queue')) {
                    // Update the log header to show queue status
                    logTitle.textContent = 'Reprocess Progress - ' + message.split('(')[1]?.split(')')[0] || 'Queued';
                }
                logContent.textContent += message + '\n';
                logContent.scrollTop = logContent.scrollHeight;
            };

            eventSource.onerror = function() {
                eventSource.close();

                // Check if the last message indicates more cases in queue
                const lastMessage = logContent.textContent.trim().split('\n').pop();
                const casesRemaining = lastMessage.includes('remaining') ||
                                       lastMessage.includes('Starting next case in queue') ||
                                       lastMessage.includes('Processing complete') && lastMessage.includes('cases remaining');

                logContent.textContent += 'Reprocess complete for this case.\n';

                // Keep all logs visible, but mark this one as complete
                if (casesRemaining) {
                    logContent.textContent += 'Continuing with next case in queue...\n';
                } else {
                    logContent.textContent += 'Reprocess complete.\n';
                }

                // Fetch only this specific case's updated data
                const params = new URLSearchParams();
                params.append('case_id', caseId); // Add specific case ID

                // Update only this case
                fetch(`/api/cases?${params.toString()}`, { cache: 'no-cache' }) // Added cache: 'no-cache'
                    .then(response => response.json())
                    .then(responseData => { // Renamed to avoid conflict with outer 'data'
                        if (responseData.data && responseData.data.length > 0) {
                            const updatedCase = responseData.data[0];
                            // Ensure we got the correct case back (should be guaranteed by backend fix)
                            if (updatedCase.id === caseId) {

                                // Update proposed plaintiff names if present in the response
                                if (responseData.proposed_names) {
                                    // Merge the received proposed names into the existing global map.
                                    // This is safer whether responseData.proposed_names is a full or partial map.
                                    Object.assign(proposedPlaintiffNames, responseData.proposed_names);
                                    console.log('Merged proposed plaintiff names after single case reprocess', proposedPlaintiffNames);
                                }

                                // Find the existing card in the DOM
                                const existingCard = document.querySelector(`.case-card[data-case-id="${caseId}"]`);
                                if (existingCard) {
                                    // Detach the current reprocessing log before replacing the card
                                    const detachedLog = existingCard.querySelector(`#${progressLogId}`);
                                    if (detachedLog) {
                                        detachedLog.remove(); // Remove from old card
                                    }

                                    // Update the case data in memory (optional, but good practice)
                                    const index = currentData.findIndex(c => c.id === caseId);
                                    if (index !== -1) {
                                        currentData[index] = updatedCase;
                                    }

                                    // Render a new card with the updated data (will use updated proposedPlaintiffNames)
                                    const newCard = renderSingleCaseCard(updatedCase);

                                    // Replace the old card with the new one
                                    existingCard.replaceWith(newCard);

                                    // Re-attach the detached log to the new card
                                    if (detachedLog) {
                                        const newDetailsRow = newCard.querySelector('.case-details-row');
                                        if (newDetailsRow && newDetailsRow.nextSibling) {
                                            newCard.insertBefore(detachedLog, newDetailsRow.nextSibling);
                                        } else {
                                            newCard.appendChild(detachedLog);
                                        }
                                    }

                                    console.log(`Case ${caseId} card replaced successfully`);
                                } else {
                                    console.warn(`Could not find existing card for case ID ${caseId} to replace.`);
                                }
                            } else {
                                console.warn(`Fetched data for case ID ${updatedCase.id} but expected ${caseId}. Cannot update card.`);
                                logContent.textContent += `Warning: Fetched data for wrong case ID (${updatedCase.id}).\n`;
                            }
                        } else {
                             console.warn(`No data returned for case ID ${caseId} after reprocess.`);
                             logContent.textContent += `Warning: No updated data found for case ${caseId}.\n`;
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching updated case:', error);
                        logContent.textContent += `Error refreshing case data: ${error.message}\n`;
                    });
            };
        })
        .catch(error => {
            console.error('Error reprocessing case:', error);
            logContent.textContent += `Error: ${error.message}\n`;
        });
    }

    // Functions
    function updateFilters() {
        currentFilters.case_number = document.getElementById('case_number').value;
        currentFilters.plaintiff_name = document.getElementById('plaintiff_name').value;
        currentFilters.plaintiff_id = document.getElementById('plaintiff_id').value;
        currentFilters.validation_status = document.getElementById('validation_status').value;
        currentFilters.ip_source = document.getElementById('ip_source').value;

        // Get selected case types from checkboxes
        currentFilters.case_type = [];
        caseTypeCheckboxes.forEach(checkbox => {
            if (checkbox.checked) {
                currentFilters.case_type.push(checkbox.value);
            }
        });

        currentFilters.picture_type = document.getElementById('picture_type').value;
        currentFilters.date_from = document.getElementById('date_from').value;
        currentFilters.date_to = document.getElementById('date_to').value;
        currentFilters.sort_by = document.getElementById('sort_by').value;
        currentFilters.sort_order = document.getElementById('sort_order').value;
        currentFilters.limit = parseInt(document.getElementById('limit').value);
        currentFilters.offset = 0;
    }

    function fetchCases(refresh = false) {
        // Show loading state
        casesContainer.innerHTML = '<div class="loading">Loading cases...</div>';

        // Build query string
        const params = new URLSearchParams();

        // Add all simple params
        for (const [key, value] of Object.entries(currentFilters)) {
            if (value !== '' && !Array.isArray(value)) {
                params.append(key, value);
            }
        }

        // Handle arrays specially
        if (currentFilters.case_type.length > 0) {
            currentFilters.case_type.forEach(type => {
                params.append('case_type[]', type);
            });
        }

        if (refresh) {
            params.append('refresh', 'true');
        }

        // Fetch data
        fetch(`/api/cases?${params.toString()}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                // View the raw response text first
                return response.text().then(text => {
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        console.error('JSON parse error:', e);
                        // Find where NaN appears in the response
                        console.log('NaN position:', text.indexOf('NaN'));
                        // Show context around the problematic area
                        const errorPos = text.indexOf('NaN');
                        const context = text.substring(Math.max(0, errorPos - 50), Math.min(text.length, errorPos + 50));
                        console.log('Context around error:', context);
                        throw e;
                    }
                });
            })
            .then(data => {
                currentData = data.data;
                totalResultsEl.textContent = `${data.total} results`;

                // Update proposed plaintiff names if included in the response
                if (data.proposed_names) {
                    proposedPlaintiffNames = data.proposed_names;

                    // Dispatch event to notify the app that cases have been loaded with proposed names
                    const event = new CustomEvent('casesLoaded', {
                        detail: {
                            proposed_names: data.proposed_names
                        }
                    });
                    casesContainer.dispatchEvent(event);
                }

                if (data.last_refresh) {
                    const refreshDate = new Date(data.last_refresh);
                    lastRefreshEl.textContent = `Last refresh: ${refreshDate.toLocaleString()}`;

                    // Check if refresh time is older than 30 minutes
                    const now = new Date();
                    const diffInMinutes = (now.getTime() - refreshDate.getTime()) / (1000 * 60);

                    if (diffInMinutes > 30) {
                        lastRefreshEl.style.color = '#a00'; // Dark red color
                    } else {
                        lastRefreshEl.style.color = ''; // Reset to default color
                    }
                } else {
                    lastRefreshEl.textContent = 'Last refresh: Unknown';
                    lastRefreshEl.style.color = ''; // Reset color if no refresh data
                }

                // Update pagination
                totalPages = Math.ceil(data.total / currentFilters.limit);
                pageInfoEl.textContent = `Page ${currentPage} of ${totalPages || 1}`;
                prevPageBtn.disabled = currentPage <= 1;
                nextPageBtn.disabled = currentPage >= totalPages || totalPages === 0;

                // Render cases
                renderCases(currentData);
            })
            .catch(error => {
                console.error('Error fetching cases:', error);
                casesContainer.innerHTML = `<div class="error">Error loading cases: ${error.message}</div>`;
            });
    }

    function renderCases(cases) {
        console.log('Rendering cases with proposed names:', proposedPlaintiffNames);

        if (cases.length === 0) {
            casesContainer.innerHTML = '<div class="no-results">No cases match your search criteria</div>';
            return;
        }

        casesContainer.innerHTML = '';

        cases.forEach(caseData => {
            const caseCard = renderSingleCaseCard(caseData);
            casesContainer.appendChild(caseCard);
        });
    }

    function renderSingleCaseCard(caseData) {
        // --- Start of extracted logic ---
        console.log(`Processing case ${caseData.id} with plaintiff ${caseData.plaintiff_name}`);

        const caseCard = document.createElement('div');
        caseCard.className = 'card case-card';
        caseCard.dataset.caseId = caseData.id;

        // Case header
        const caseHeader = document.createElement('div');
        caseHeader.className = 'case-header';

        // Add update button
        const updateButton = document.createElement('button');
        updateButton.className = 'update-case-btn';
        updateButton.innerHTML = '<i class="fas fa-sync-alt"></i>';
        updateButton.title = 'Update this case';
        updateButton.dataset.caseId = caseData.id;
        updateButton.addEventListener('click', function() {
            console.log('Update button clicked for case:', caseData.id);
            showUpdateCaseOverlay(caseData.id);
        });
        caseHeader.appendChild(updateButton);

        // Store case ID for reference
        const caseIdEl = document.createElement('span');
        caseIdEl.className = 'case-id';
        caseIdEl.style.display = 'none'; // Hidden
        caseIdEl.textContent = caseData.id;
        caseHeader.appendChild(caseIdEl);

        // Case title with plaintiff name
        const caseTitleContainer = document.createElement('div');
        caseTitleContainer.className = 'case-title-container';
        caseTitleContainer.style.display = 'flex';
        caseTitleContainer.style.alignItems = 'center';
        caseTitleContainer.style.gap = '8px';
        caseTitleContainer.style.flexGrow = '1';

        const caseTitle = document.createElement('div');
        caseTitle.className = 'case-title';
        const plaintiffName = caseData.plaintiff_name || 'Unknown Plaintiff';

        // Check if there's a proposed name for this case
        const hasProposedName = proposedPlaintiffNames &&
                                proposedPlaintiffNames[caseData.id] &&
                                proposedPlaintiffNames[caseData.id] !== plaintiffName;

        console.log(`Case ${caseData.id}: Has proposed name? ${hasProposedName}`,
                    hasProposedName ? proposedPlaintiffNames[caseData.id] : '');

        if (hasProposedName) {
            caseTitle.innerHTML = `${plaintiffName} <span class="proposed-name"><a href="/plaintiff_review">(New proposed name: ${proposedPlaintiffNames[caseData.id]})</a></span>`;
        } else {
            caseTitle.textContent = plaintiffName;
        }

        caseTitleContainer.appendChild(caseTitle);

        // Add improve plaintiff button directly next to the title
        const improveBtn = document.createElement('button');
        improveBtn.className = 'improve-plaintiff-btn';
        improveBtn.innerHTML = '<i class="fas fa-wand-magic-sparkles"></i>';
        improveBtn.title = 'Improve Plaintiff Name';
        improveBtn.dataset.caseId = caseData.id;
        improveBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            improvePlaintiffName(this);
        });
        caseTitleContainer.appendChild(improveBtn);

        caseHeader.appendChild(caseTitleContainer);

        // Add validation icons
        const validationIcons = document.createElement('div');
        validationIcons.className = 'validation-icons';

        // Get current validation status
        const validationStatus = caseData.validation_status || '';

        // Create validated icon
        const validatedIcon = document.createElement('div');
        validatedIcon.className = `validation-icon ${validationStatus === 'validated' ? 'active validated' : ''}`;
        validatedIcon.innerHTML = '<i class="fas fa-check"></i>';
        validatedIcon.title = 'Validated';
        validatedIcon.dataset.status = 'validated';
        validatedIcon.dataset.caseId = caseData.id;

        // Create review required icon
        const reviewIcon = document.createElement('div');
        reviewIcon.className = `validation-icon ${validationStatus === 'review_required' ? 'active review' : ''}`;
        reviewIcon.innerHTML = '<i class="far fa-lightbulb"></i>';
        reviewIcon.title = 'Review Required';
        reviewIcon.dataset.status = 'review_required';
        reviewIcon.dataset.caseId = caseData.id;

        // Create failed icon
        const failedIcon = document.createElement('div');
        failedIcon.className = `validation-icon ${validationStatus === 'failed' ? 'active failed' : ''}`;
        failedIcon.innerHTML = '<i class="fas fa-times"></i>';
        failedIcon.title = 'Failed';
        failedIcon.dataset.status = 'failed';
        failedIcon.dataset.caseId = caseData.id;

        // Add event listeners to icons
        [validatedIcon, reviewIcon, failedIcon].forEach(icon => {
            icon.addEventListener('click', function() {
                const newStatus = this.dataset.status;
                const caseId = this.dataset.caseId;
                const currentStatus = caseData.validation_status || '';

                // Toggle status if already active
                const statusToSend = currentStatus === newStatus ? '' : newStatus;

                updateValidationStatus(caseId, statusToSend);
            });
        });

        validationIcons.appendChild(validatedIcon);
        validationIcons.appendChild(reviewIcon);
        validationIcons.appendChild(failedIcon);

        caseHeader.appendChild(validationIcons);

        // Case Details Row
        const caseDetailsRow = document.createElement('div');
        caseDetailsRow.className = 'case-details-row';

        // Left side: Basic Info
        const caseInfoLeft = document.createElement('div');
        caseInfoLeft.className = 'case-info-left';

        // Format date
        let dateFiled = caseData.date_filed || '';
        let formattedDateForPath = '';
        if (dateFiled) {
            try {
                const dateObj = new Date(dateFiled);
                if (!isNaN(dateObj.getTime())) {
                    // Format as YYYY-MM-DD
                    dateFiled = dateObj.toISOString().split('T')[0];
                    formattedDateForPath = dateFiled;
                } else {
                    dateFiled = caseData.date_filed;
                }
            } catch (e) {
                console.warn("Error parsing date:", dateFiled, e);
                dateFiled = caseData.date_filed || 'N/A';
            }
        } else {
            dateFiled = 'N/A';
        }

        addInfoItem(caseInfoLeft, 'Case #:', `(${caseData.id}) ${caseData.docket || 'N/A'}`);
        addInfoItem(caseInfoLeft, 'Court:', caseData.court || 'N/A');
        addInfoItem(caseInfoLeft, 'Type:', caseData.nos_description || 'N/A');
        addInfoItem(caseInfoLeft, 'Status:', caseData.class_code || 'N/A');
        addInfoItem(caseInfoLeft, 'Filed:', dateFiled);
        addInfoItem(caseInfoLeft, 'Plaintiff ID:', caseData.plaintiff_id || 'N/A');
        addStepsInfoItem(caseInfoLeft, caseData.id);

        // Add update_time, formatted
        let updateTime = caseData.update_time || '';
        if (updateTime) {
            try {
                const dateObj = new Date(updateTime);
                if (!isNaN(dateObj.getTime())) {
                    const year = dateObj.getFullYear();
                    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
                    const day = String(dateObj.getDate()).padStart(2, '0');
                    const hours = String(dateObj.getHours()).padStart(2, '0');
                    const minutes = String(dateObj.getMinutes()).padStart(2, '0');
                    updateTime = `${year}-${month}-${day} ${hours}:${minutes}`; // Format as YYYY-MM-DD HH:MM
                } else {
                    updateTime = caseData.update_time; // Keep original if parsing failed
                }
            } catch (e) {
                console.warn("Error parsing update_time:", updateTime, e);
                updateTime = caseData.update_time || 'N/A';
            }
        } else {
            updateTime = 'N/A';
        }
        // Make the "Updated:" field clickable to show historical logs
        const updatedItem = addInfoItem(caseInfoLeft, 'Updated:', updateTime);
        if (updatedItem && caseData.id) { // Ensure item and caseId exist
            updatedItem.classList.add('updated-timestamp-item'); // Add a class for styling/selection
            updatedItem.style.cursor = 'pointer'; // Indicate it's clickable
            updatedItem.title = 'Click to view historical logs';
            updatedItem.dataset.caseId = caseData.id; // Store caseId for the event listener
            updatedItem.addEventListener('click', function() {
                const caseId = this.dataset.caseId;
                const caseCardElement = this.closest('.case-card');
                showHistoricalLogs(caseId, caseCardElement);
            });
        }

        // Add image status info if available
        if (caseData.images_status) {
            const steps = caseData.images_status.steps_processed || [];
            addInfoItem(caseInfoLeft, 'Steps:', steps.length > 0 ? steps.join(', ') : 'None');
            addInfoItem(caseInfoLeft, 'PDFs:', caseData.images_status.number_of_pdfs || 0);

            const nextFetch = caseData.images_status.scheduled_next_fetch;
            if (nextFetch) {
                addInfoItem(caseInfoLeft, 'Next Fetch:', new Date(nextFetch).toLocaleDateString());
            }
        }

        // Right side: Action Links
        const caseInfoRight = document.createElement('div');
        caseInfoRight.className = 'case-info-right';

        // 1. Langfuse Link
        const langfuseLink = document.createElement('a');
        langfuseLink.textContent = 'Langfuse';
        langfuseLink.className = 'action-link';
        const traceUrl = caseData.images_status?.trace_url;
        if (traceUrl) {
            langfuseLink.href = traceUrl;
            langfuseLink.target = '_blank';
            langfuseLink.title = 'View on Langfuse';
        } else {
            langfuseLink.href = '#';
            langfuseLink.classList.add('disabled');
            langfuseLink.title = 'Langfuse link not available';
            langfuseLink.onclick = (e) => e.preventDefault();
        }
        caseInfoRight.appendChild(langfuseLink);

        // 1.5. LexisNexis (LN) Link
        const lnLink = document.createElement('a');
        lnLink.textContent = 'LN';
        lnLink.className = 'action-link';
        
        const lnUrl = caseData.ln_url;
        if (lnUrl) {
            lnLink.href = lnUrl;
            lnLink.target = '_blank';
            lnLink.title = 'View on LexisNexis';
        } else {
            lnLink.href = '#';
            lnLink.classList.add('disabled');
            lnLink.title = 'LexisNexis link not available';
            lnLink.onclick = (e) => e.preventDefault();
        }
        caseInfoRight.appendChild(lnLink);

        // 2. File Link
        const fileLink = document.createElement('a');
        fileLink.textContent = 'Files';
        fileLink.className = 'action-link';
        fileLink.target = '_blank';

        const docket = caseData.docket;
        if (formattedDateForPath && docket) {
            const formattedDocket = docket.replace(/:/g, '_');
            const filePath = `/Maidalv/IP TRO Data/Case Files/${formattedDateForPath} - ${formattedDocket}/`;

            // First, URL-encode the path itself
            const singleEncodedPath = encodeURIComponent(filePath);

            // Then, create the parameter value string "openfile=<encoded_path>"
            const paramValue = `openfile=${singleEncodedPath}`;

            // Finally, URL-encode this *entire* parameter value string
            const fullyEncodedParam = encodeURIComponent(paramValue);

            // Construct the final URL
            const fileUrl = `https://synology.jslawlegal.com/index.cgi?launchApp=SYNO.SDS.App.FileStation3.Instance&launchParam=${fullyEncodedParam}`;

            fileLink.href = fileUrl;
            fileLink.title = `Open case files folder: ${filePath}`;
        } else {
            fileLink.href = '#';
            fileLink.classList.add('disabled');
            fileLink.title = 'Cannot generate file link (missing date or docket)';
            fileLink.onclick = (e) => e.preventDefault();
        }
        caseInfoRight.appendChild(fileLink);

        // 3. DocketBird (DBird) Link
        const dBirdLink = document.createElement('a');
        dBirdLink.textContent = 'DBird';
        dBirdLink.className = 'action-link';
        dBirdLink.target = '_blank';

        let dBirdCourtPrefix = null;
        let dBirdDocket = null;
        const court = caseData.court; // e.g., "Texas Eastern"
        const originalDocket = caseData.docket; // e.g., "1:25-cv-00097"

        // Process Court
        if (court && typeof court === 'string') {
            const words = court.split(' ');
            if (words.length >= 2 && words[0].length >= 2 && words[1].length >= 1) {
                dBirdCourtPrefix = (words[0].substring(0, 2) + words[1].substring(0, 1) + 'd').toLowerCase(); // e.g., txed
            } else {
                console.warn(`Court format not recognized for DBird link: ${court}`);
            }
        }

        // Process Docket
        if (originalDocket && typeof originalDocket === 'string') {
                // Match pattern like '1:25-cv-00097' to insert '20'
                const match = originalDocket.match(/^([^:]*:)(\d{2})(-cv-\d+)$/);
                if (match && match.length === 4) {
                    const prefix = match[1]; // e.g., '1:'
                    const year = match[2]; // e.g., '25'
                    const suffix = match[3]; // e.g., '-cv-00097'
                    // Prepend '20' to the two-digit year
                    dBirdDocket = `${prefix}20${year}${suffix}`; // e.g., '1:2025-cv-00097'
                } else {
                    console.warn(`Docket format not recognized for DBird link year insertion: ${originalDocket}`);
                    // Fallback: Use original docket if format is unexpected, maybe still useful
                    // dBirdDocket = originalDocket;
                }
        }

        if (dBirdCourtPrefix && dBirdDocket) {
            const dBirdCaseId = `${dBirdCourtPrefix}-${dBirdDocket}`;
            const dBirdUrl = `https://www.docketbird.com/cases?case_id=${encodeURIComponent(dBirdCaseId)}`;
            dBirdLink.href = dBirdUrl;
            dBirdLink.title = `View on DocketBird: ${dBirdCaseId}`;
        } else {
            dBirdLink.href = '#';
            dBirdLink.classList.add('disabled');
            dBirdLink.title = 'Cannot generate DocketBird link (missing/invalid court or docket format)';
            dBirdLink.onclick = (e) => e.preventDefault();
        }
        caseInfoRight.appendChild(dBirdLink);

        // 4. Multi-website search links
        let searchDocket = null;
        // Format case number for search
        if (originalDocket && typeof originalDocket === 'string') {
            try {
                // Match pattern like '1:25-cv-00097'
                const match = originalDocket.match(/^[^:]*:(.+?-cv-)(\d+)$/);
                if (match && match.length === 3) {
                    const prefix = match[1]; // e.g., '25-cv-'
                    const numberPart = match[2]; // e.g., '00097'
                    // Remove leading zeros from the number part
                    const number = parseInt(numberPart, 10);
                    if (!isNaN(number)) {
                        searchDocket = prefix + number.toString(); // e.g., '25-cv-97'
                    }
                } else {
                        console.warn(`Docket format not recognized for search links: ${originalDocket}`);
                }
            } catch (e) {
                console.error(`Error processing docket for search links: ${originalDocket}`, e);
            }
        }

        // Create the multi-site search links
        createMultiSiteSearchLinks(caseInfoRight, searchDocket);

        // Append left and right to the details row
        caseDetailsRow.appendChild(caseInfoLeft);
        caseDetailsRow.appendChild(caseInfoRight);

        // Case Description
        let caseDescription = document.createElement('div');
        caseDescription.className = 'case-description';

        // Try to parse plaintiff overview
        let plaintiffOverview = '';
        if (caseData.plaintiff_overview) {
            try {
                if (typeof caseData.plaintiff_overview === 'string') {
                    const parsed = JSON.parse(caseData.plaintiff_overview);
                    plaintiffOverview = parsed.Chinese || parsed.English || '';
                } else if (caseData.plaintiff_overview.Chinese || caseData.plaintiff_overview.English) {
                    plaintiffOverview = caseData.plaintiff_overview.Chinese || caseData.plaintiff_overview.English;
                }
            } catch (e) {
                plaintiffOverview = caseData.plaintiff_overview;
            }
        }

        // Try to parse AI summary
        let aiSummary = '';
        if (caseData.aisummary) {
            try {
                if (typeof caseData.aisummary === 'string') {
                    const parsed = JSON.parse(caseData.aisummary);
                    aiSummary = parsed.Chinese || parsed.English || '';
                } else if (caseData.aisummary.Chinese || caseData.aisummary.English) {
                    aiSummary = caseData.aisummary.Chinese || caseData.aisummary.English;
                }
            } catch (e) {
                aiSummary = caseData.aisummary;
            }
        }

        if (plaintiffOverview && plaintiffOverview !== '未知') {
            caseDescription.innerHTML += `<div><strong>Plaintiff:</strong> ${plaintiffOverview}</div>`;
        }

        if (aiSummary) {
            caseDescription.innerHTML += `<div><strong>Case:</strong> ${aiSummary}</div>`;
        }

        // Construct the full case card
        caseCard.appendChild(caseHeader);
        caseCard.appendChild(caseDetailsRow);
        if (caseDescription.innerHTML.trim() !== '') {
            caseCard.appendChild(caseDescription);
        }

        // IP sections
        const ipTypes = [
            { key: 'trademarks', name: 'Trademarks', statusKey: 'trademark_status' },
            { key: 'patents', name: 'Patents', statusKey: 'patent_status' },
            { key: 'copyrights', name: 'Copyrights', statusKey: 'copyright_status' }
        ];

        ipTypes.forEach(type => {
            if (caseData[type.key] && caseData[type.key].length > 0) {
                const ipSection = document.createElement('div');
                ipSection.className = 'ip-section';

                const ipTitleContainer = document.createElement('div');
                ipTitleContainer.className = 'ip-title';

                const ipTitleText = document.createElement('div');
                ipTitleText.className = 'ip-title-text';
                ipTitleText.textContent = type.name;
                ipTitleContainer.appendChild(ipTitleText);

                // Add IP status information
                if (caseData.images_status && caseData.images_status[type.statusKey]) {
                    const statusContainer = document.createElement('div');
                    statusContainer.className = 'ip-status';

                    const statusData = caseData.images_status[type.statusKey];

                    // Add status items with non-zero counts
                    const statusSources = [
                        { key: 'exhibit', label: 'Exhibit' },
                        { key: 'byregno', label: 'By Reg#' },
                        { key: 'cn_website', label: 'CN Website' },
                        { key: 'byname', label: 'By Name' },
                        { key: 'bygoogle', label: 'By Google' },
                        { key: 'manual', label: 'Manual' }
                    ];

                    statusSources.forEach(source => {
                        if (statusData[source.key] && statusData[source.key].count > 0) {
                            const statusItem = document.createElement('div');
                            statusItem.className = 'ip-status-item';

                            let statusText = `${source.label}: <span class="ip-status-item-count">${statusData[source.key].count}</span>`;

                            // Add additional source information if available
                            if (source.key === 'byname' && statusData[source.key].search_term_used) {
                                statusText += ` (${statusData[source.key].search_term_used})`;
                            }

                            statusItem.innerHTML = statusText;
                            statusContainer.appendChild(statusItem);
                        }
                    });

                    // Only add status container if it has children
                    if (statusContainer.children.length > 0) {
                        ipTitleContainer.appendChild(statusContainer);
                    }
                }

                ipSection.appendChild(ipTitleContainer);

                const ipItems = document.createElement('div');
                ipItems.className = 'ip-items';

                caseData[type.key].forEach(item => {
                    const ipItem = document.createElement('div');
                    ipItem.className = 'ip-item';

                    // Image
                    const imgUrl = getImagePath(caseData.plaintiff_id, item.image, type.key);
                    const img = document.createElement('img');
                    img.className = 'ip-item-image';
                    img.src = imgUrl;
                    img.alt = type.name;
                    img.onerror = function() {
                        const debugUrl = this.src;
                        console.log("Image not found:", debugUrl);
                        this.style.padding = '10px';
                        this.style.backgroundColor = '#f8d7da';
                        this.style.border = '1px solid #f5c6cb';
                        this.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2Y4ZDdkYSIvPjx0ZXh0IHg9IjUwIiB5PSI0MCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEwIiBmaWxsPSIjNzIxYzI0IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+SW1hZ2UgTm90IEZvdW5kPC90ZXh0Pjx0ZXh0IHg9IjUwIiB5PSI2MCIgZm9udC1mYW1pbHk9Im1vbm9zcGFjZSIgZm9udC1zaXplPSI4IiBmaWxsPSIjNzIxYzI0IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+Q2xpY2sgdG8gc2VlIFVSTDwvdGV4dD48L3N2Zz4=';

                        // Make the image clickable to show URL
                        this.style.cursor = 'pointer';
                        this.onclick = function(e) {
                            e.stopPropagation(); // Prevent triggering the preview
                            const urlDialog = document.createElement('div');
                            urlDialog.style.position = 'fixed';
                            urlDialog.style.top = '50%';
                            urlDialog.style.left = '50%';
                            urlDialog.style.transform = 'translate(-50%, -50%)';
                            urlDialog.style.backgroundColor = 'white';
                            urlDialog.style.padding = '20px';
                            urlDialog.style.borderRadius = '5px';
                            urlDialog.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
                            urlDialog.style.zIndex = '1000';
                            urlDialog.style.maxWidth = '80%';
                            urlDialog.style.wordBreak = 'break-all';

                            const urlText = document.createElement('pre');
                            urlText.style.margin = '0 0 10px 0';
                            urlText.style.whiteSpace = 'pre-wrap';
                            urlText.textContent = debugUrl;

                            const copyButton = document.createElement('button');
                            copyButton.textContent = 'Copy URL';
                            copyButton.className = 'btn primary';
                            copyButton.onclick = () => {
                                navigator.clipboard.writeText(debugUrl);
                                copyButton.textContent = 'Copied!';
                                setTimeout(() => copyButton.textContent = 'Copy URL', 2000);
                            };

                            const closeButton = document.createElement('button');
                            closeButton.textContent = 'Close';
                            closeButton.className = 'btn secondary';
                            closeButton.style.marginLeft = '10px';
                            closeButton.onclick = () => document.body.removeChild(urlDialog);

                            urlDialog.appendChild(urlText);
                            urlDialog.appendChild(copyButton);
                            urlDialog.appendChild(closeButton);
                            document.body.appendChild(urlDialog);
                        };
                    };

                    // Add click handler for image preview (using common function)
                    // Always preview high quality for the item's own image
                    img.addEventListener('click', function() {
                        const highQualityItemUrl = `http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/${caseData.plaintiff_id}/high/${item.image}`;
                        previewImage(highQualityItemUrl);
                    });

                    ipItem.appendChild(img);

                    // IP info
                    const ipItemInfo = document.createElement('div');
                    ipItemInfo.className = 'ip-item-info';

                    // Add specific fields based on IP type
                    const fullFilenames = item.fullFilename;
                    if (type.key === 'trademarks') {
                        const trademarkText = Array.isArray(item.trademarkText) ? item.trademarkText.join(', ') : item.trademarkText;
                        const regNo = Array.isArray(item.regNo) ? item.regNo.join(', ') : item.regNo;
                        const intClsList = Array.isArray(item.intClsList) ? item.intClsList.join(', ') : (item.intClsList || 'N/A');
                        const fullFilenames = item.fullFilename;
                        
                        addIpInfoItem(ipItemInfo, 'Keywords:', trademarkText || 'N/A');
                        // Always add the Reg # item, then potentially make the value a link
                        const regNoInfoItem = addIpInfoItem(ipItemInfo, 'Reg #:', regNo || 'N/A');
                        addIpInfoItem(ipItemInfo, 'Class #:', intClsList || 'N/A');
                        makeInfoItemValueLinkable(regNoInfoItem, caseData.plaintiff_id, 'Reg #:', item.regNo, fullFilenames);

                    } else if (type.key === 'patents') {
                        addIpInfoItem(ipItemInfo, 'Name:', item.productName || 'N/A');
                        addIpInfoItem(ipItemInfo, 'Applicant:', item.applicant || 'N/A');
                        addIpInfoItem(ipItemInfo, 'Inventors:', item.inventors || 'N/A');
                        addIpInfoItem(ipItemInfo, 'Assignee:', item.assignee || 'N/A');
                        const patentNoInfoItem = addIpInfoItem(ipItemInfo, 'Patent #:', item.patentNumber || 'N/A');
                    } else if (type.key === 'copyrights') {
                        const regNo = Array.isArray(item.regNo) ? item.regNo.join(', ') : item.regNo;
                        const regNoInfoItem = addIpInfoItem(ipItemInfo, 'Reg #:', regNo || 'N/A');
                        makeInfoItemValueLinkable(regNoInfoItem, caseData.plaintiff_id, 'Reg #:', item.regNo, item.fullFilename);
                    }

                    ipItem.appendChild(ipItemInfo);
                    ipItems.appendChild(ipItem);
                });

                ipSection.appendChild(ipItems);
                caseCard.appendChild(ipSection);
            }
        });

        // --- End of extracted logic ---
        return caseCard;
    }

    // addInfoItem is available in common.js (or adapt if specific logic needed)

    // Add a special clickable info item for steps
    function addStepsInfoItem(container, caseId) {
        const item = document.createElement('div');
        item.className = 'info-item steps-info-item';
        item.setAttribute('data-case-id', caseId);

        const labelEl = document.createElement('span');
        labelEl.className = 'info-label';
        labelEl.textContent = '# Steps:';

        const valueEl = document.createElement('span');
        valueEl.className = 'steps-count';
        valueEl.textContent = '?'; // Question mark instead of dash

        const loadingEl = document.createElement('span');
        loadingEl.className = 'steps-loading';
        loadingEl.style.display = 'none';
        loadingEl.innerHTML = ' <i class="fas fa-spinner fa-spin"></i>';

        item.appendChild(labelEl);
        item.appendChild(valueEl);
        item.appendChild(loadingEl);

        // Add click handler to load steps
        item.addEventListener('click', function() {
            const caseId = this.getAttribute('data-case-id');
            const stepsSection = document.getElementById(`steps-section-${caseId}`);

            if (stepsSection) {
                // Toggle visibility if section already exists
                stepsSection.style.display = stepsSection.style.display === 'none' ? 'block' : 'none';
            } else {
                // Fetch steps data
                fetchCaseSteps(caseId);
            }
        });

        container.appendChild(item);
        return item;
    }

    // Function to fetch steps for a case
    function fetchCaseSteps(caseId) {
        // Find the steps info item and show loading indicator
        const stepsInfoItem = document.querySelector(`.steps-info-item[data-case-id="${caseId}"]`);
        if (stepsInfoItem) {
            stepsInfoItem.querySelector('.steps-loading').style.display = 'inline';
        }

        // Fetch steps data from API
        fetch(`/api/cases/${caseId}/steps`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                // Check for duplicate step numbers
                const stepNumbersCount = {};
                let hasDuplicates = false;

                data.steps.forEach(step => {
                    if (step.step_nb !== null) {
                        const stepNumber = Number(step.step_nb);
                        const stepKey = isNaN(stepNumber) ? step.step_nb : stepNumber.toString();
                        stepNumbersCount[stepKey] = (stepNumbersCount[stepKey] || 0) + 1;
                        if (stepNumbersCount[stepKey] > 1) {
                            hasDuplicates = true;
                        }
                    }
                });

                // Hide loading indicator and update steps count
                if (stepsInfoItem) {
                    const stepsCount = stepsInfoItem.querySelector('.steps-count');
                    stepsInfoItem.querySelector('.steps-loading').style.display = 'none';
                    stepsCount.textContent = data.steps.length;

                    // Add duplicate class if duplicates found
                    if (hasDuplicates) {
                        stepsCount.classList.add('duplicate');
                    } else {
                        stepsCount.classList.remove('duplicate');
                    }
                }

                // Find the case card for this case
                const caseCard = stepsInfoItem.closest('.case-card');
                if (caseCard) {
                    // Create steps section
                    displayCaseSteps(caseCard, caseId, data.steps, stepNumbersCount);
                }
            })
            .catch(error => {
                console.error('Error fetching steps:', error);
                // Hide loading indicator and show error
                if (stepsInfoItem) {
                    stepsInfoItem.querySelector('.steps-loading').style.display = 'none';
                    stepsInfoItem.querySelector('.steps-count').textContent = 'Error';
                }
            });
    }

    // Function to display steps data in the case card
    function displayCaseSteps(caseCard, caseId, steps, stepNumbersCount) {
        // Find the right position to insert steps section (after details row and before plaintiff overview)
        const caseDescription = caseCard.querySelector('.case-description');

        // Create steps section
        const stepsSection = document.createElement('div');
        stepsSection.className = 'log-section';
        stepsSection.id = `steps-section-${caseId}`;

        // Create header with close button
        const stepsSectionHeader = document.createElement('div');
        stepsSectionHeader.className = 'log-section-header';

        const stepsSectionTitle = document.createElement('div');
        stepsSectionTitle.className = 'log-section-title';
        stepsSectionTitle.textContent = 'Case Steps';
        stepsSectionHeader.appendChild(stepsSectionTitle);

        const closeButton = document.createElement('button');
        closeButton.className = 'close-log';
        closeButton.innerHTML = '<i class="fas fa-times"></i>';
        closeButton.title = 'Close Steps';
        closeButton.addEventListener('click', function() {
            stepsSection.style.display = 'none';
        });
        stepsSectionHeader.appendChild(closeButton);

        stepsSection.appendChild(stepsSectionHeader);

        // Create steps table
        const stepsTable = document.createElement('table');
        stepsTable.className = 'steps-table';

        // Table header
        const tableHeader = document.createElement('thead');
        const headerRow = document.createElement('tr');

        const headers = ['Date Filed', 'Step #', 'Proceeding Text', 'Chinese', 'Files Downloaded', 'Files Failed', 'Last Updated'];
        headers.forEach(headerText => {
            const th = document.createElement('th');
            th.textContent = headerText;
            headerRow.appendChild(th);
        });

        tableHeader.appendChild(headerRow);
        stepsTable.appendChild(tableHeader);

        // Table body
        const tableBody = document.createElement('tbody');

        steps.forEach(step => {
            const row = document.createElement('tr');

            // Date Filed - Format as YYYY-MM-DD
            const dateCell = document.createElement('td');
            if (step.step_date_filed) {
                try {
                    const date = new Date(step.step_date_filed);
                    if (!isNaN(date.getTime())) {
                        // Format as YYYY-MM-DD
                        const year = date.getFullYear();
                        const month = String(date.getMonth() + 1).padStart(2, '0');
                        const day = String(date.getDate()).padStart(2, '0');
                        dateCell.textContent = `${year}-${month}-${day}`;
                    } else {
                        dateCell.textContent = step.step_date_filed;
                    }
                } catch (e) {
                    dateCell.textContent = step.step_date_filed;
                }
            } else {
                dateCell.textContent = '-';
            }
            row.appendChild(dateCell);

            // Step Number - Remove decimal places and highlight duplicates
            const stepNbCell = document.createElement('td');
            if (step.step_nb !== null) {
                // Convert to number and then to string to remove trailing zeros
                const stepNumber = Number(step.step_nb);
                const stepText = isNaN(stepNumber) ? step.step_nb : stepNumber.toString();
                stepNbCell.textContent = stepText;

                // Check if this step number appears more than once
                const stepKey = stepText;
                if (stepNumbersCount[stepKey] > 1) {
                    stepNbCell.style.color = '#a00'; // Red color for duplicates
                }
            } else {
                stepNbCell.textContent = '-';
            }
            row.appendChild(stepNbCell);

            // Proceeding Text
            const proceedingCell = document.createElement('td');
            proceedingCell.textContent = step.proceeding_text || '-';
            row.appendChild(proceedingCell);

            // Chinese Translation
            const chineseCell = document.createElement('td');
            chineseCell.textContent = step.proceeding_text_cn || '-';
            row.appendChild(chineseCell);

            // Files Downloaded
            const filesDownloadedCell = document.createElement('td');
            filesDownloadedCell.textContent = (step.files_downloaded !== null) ? step.files_downloaded : '-';
            row.appendChild(filesDownloadedCell);

            // Files Failed
            const filesFailedCell = document.createElement('td');
            filesFailedCell.textContent = (step.files_failed !== null) ? step.files_failed : '-';
            row.appendChild(filesFailedCell);

            // Update Time
            const updateTimeCell = document.createElement('td');
            if (step.update_time) {
                try {
                    const date = new Date(step.update_time);
                    if (!isNaN(date.getTime())) {
                        // Format as YYYY-MM-DD
                        const year = date.getFullYear();
                        const month = String(date.getMonth() + 1).padStart(2, '0');
                        const day = String(date.getDate()).padStart(2, '0');
                        updateTimeCell.textContent = `${year}-${month}-${day}`;
                    } else {
                        updateTimeCell.textContent = step.update_time;
                    }
                } catch (e) {
                    updateTimeCell.textContent = step.update_time;
                }
            } else {
                updateTimeCell.textContent = '-';
            }
            row.appendChild(updateTimeCell);

            tableBody.appendChild(row);
        });

        stepsTable.appendChild(tableBody);
        stepsSection.appendChild(stepsTable);

        // Insert steps section before plaintiff overview
        if (caseDescription) {
            caseCard.insertBefore(stepsSection, caseDescription);
        } else {
            caseCard.appendChild(stepsSection);
        }
    }

    // previewImage function is available in common.js
    function updateValidationStatus(caseId, status) {
        fetch('/api/cases/validation', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                case_id: caseId,
                validation_status: status
            }),
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(data.error || 'Failed to update validation status');
                });
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // Update the UI to reflect the new status
                const validationIcons = document.querySelectorAll(`.validation-icon[data-case-id="${caseId}"]`);
                validationIcons.forEach(icon => {
                    // Remove active class from all icons
                    icon.classList.remove('active', 'validated', 'review', 'failed');

                    // Add active class to the clicked icon
                    if (icon.dataset.status === status) {
                        icon.classList.add('active');
                        if (status === 'validated') {
                            icon.classList.add('validated');
                        } else if (status === 'review_required') {
                            icon.classList.add('review');
                        } else if (status === 'failed') {
                            icon.classList.add('failed');
                        }
                    }
                });

                // Update the data in current state
                currentData.forEach(caseItem => {
                    if (caseItem.id === caseId) {
                        caseItem.validation_status = status;
                    }
                });
            } else {
                throw new Error(data.error || 'Failed to update validation status');
            }
        })
        .catch(error => {
            console.error('Error updating validation status:', error);
            alert('Failed to update validation status: ' + error.message);
            // Refresh the page or case data to ensure UI shows correct state
            location.reload();
        });
    }

    function improvePlaintiffName(button) {
        const caseId = button.dataset.caseId;
        if (!caseId) return;

        // Change button to loading spinner
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        button.disabled = true;

        // Call the API to improve plaintiff name
        fetch('/api/cases/improve-plaintiff', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ case_id: caseId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update the button to show success
                button.innerHTML = '<i class="fas fa-check"></i>';
                button.className = 'improve-plaintiff-btn success';
                button.title = 'Plaintiff name improved';

                // If a proposed name was returned, update the display
                if (data.proposed_name) {
                    // Store in our cache
                    proposedPlaintiffNames[caseId] = data.proposed_name;

                    // Find the case title element and update it
                    const caseCard = button.closest('.case-card');
                    const caseTitleElement = caseCard.querySelector('.case-title');
                    if (caseTitleElement) {
                        // Get the current plaintiff name (without any proposed name text)
                        const currentName = caseTitleElement.textContent.split('(New proposed name:')[0].trim();

                        // Update the title with the new proposed name
                        caseTitleElement.innerHTML = `${currentName} <span class="proposed-name"><a href="/plaintiff_review">(New proposed name: ${data.proposed_name})</a></span>`;
                    }
                }
            } else {
                // Show error state
                button.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
                button.className = 'improve-plaintiff-btn error';
                button.title = 'Error: ' + (data.error || 'Failed to improve name');

                // Reset after 3 seconds
                setTimeout(() => {
                    button.innerHTML = '<i class="fas fa-wand-magic-sparkles"></i>';
                    button.className = 'improve-plaintiff-btn';
                    button.title = 'Improve Plaintiff Name';
                    button.disabled = false;
                }, 3000);
            }
        })
        .catch(error => {
            console.error('Error improving plaintiff name:', error);
            button.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
            button.className = 'improve-plaintiff-btn error';
            button.title = 'Error: ' + error.message;

            // Reset after 3 seconds
            setTimeout(() => {
                button.innerHTML = '<i class="fas fa-wand-magic-sparkles"></i>';
                button.className = 'improve-plaintiff-btn';
                button.title = 'Improve Plaintiff Name';
                button.disabled = false;
            }, 3000);
        });
    }

    function addInfoItemWithElement(container, label, valueElement) {
        const item = document.createElement('div');
        item.className = 'info-item';

        const labelEl = document.createElement('span');
        labelEl.className = 'info-label';
        // Remove the colon from the label if it already has one
        const labelText = label.endsWith(':') ? label : label + ':';
        labelEl.textContent = labelText;

        item.appendChild(labelEl);
        item.appendChild(valueElement);
        container.appendChild(item);

        return item;
    }

    // Replace the SD link with multiple website search links
    // Function to create search links for all websites
    function createMultiSiteSearchLinks(container, docketFormat) {
        // Create WWW link (opens all sites in separate tabs)
        const wwwLink = document.createElement('a');
        wwwLink.textContent = 'WWW';
        wwwLink.className = 'action-link';
        wwwLink.title = 'Search across multiple websites';

        if (docketFormat) {
            // Set up the click handler to open multiple tabs
            wwwLink.href = '#';
            wwwLink.onclick = function(e) {
                e.preventDefault();
                // Open each search in a new tab
                window.open(`https://www.10100.com/search/${encodeURIComponent(docketFormat)}`, '_blank');
                window.open(`https://sellerdefense.cn/?s=${encodeURIComponent(docketFormat)}`, '_blank');
                window.open(`https://maijiazhichi.com/?s=${encodeURIComponent(docketFormat)}`, '_blank');
            };
        } else {
            wwwLink.classList.add('disabled');
            wwwLink.title = 'Cannot generate search links (invalid docket format)';
            wwwLink.onclick = (e) => e.preventDefault();
        }

        container.appendChild(wwwLink);

        // Alternative: Create individual links for each site (uncomment if needed)
        /*
        // 10100 Link
        const link10100 = document.createElement('a');
        link10100.textContent = '10100';
        link10100.className = 'action-link';
        link10100.target = '_blank';

        if (docketFormat) {
            link10100.href = `https://www.10100.com/search/${encodeURIComponent(docketFormat)}`;
            link10100.title = `Search 10100.com for ${docketFormat}`;
        } else {
            link10100.classList.add('disabled');
            link10100.onclick = (e) => e.preventDefault();
        }
        container.appendChild(link10100);

        // Maijia Link
        const linkMaijia = document.createElement('a');
        linkMaijia.textContent = 'Maijia';
        linkMaijia.className = 'action-link';
        linkMaijia.target = '_blank';

        if (docketFormat) {
            linkMaijia.href = `https://maijiazhichi.com/?s=${encodeURIComponent(docketFormat)}`;
            linkMaijia.title = `Search maijiazhichi.com for ${docketFormat}`;
        } else {
            linkMaijia.classList.add('disabled');
            linkMaijia.onclick = (e) => e.preventDefault();
        }
        container.appendChild(linkMaijia);
        */
    }
});

// Helper function to create or reuse a log section (similar to updateCase/updateCaseWithOptions)
function createOrGetLogSection(caseCard, titleText, caseIdForTitle) {
    let progressLog = caseCard.querySelector('.log-section');
    let logContentElement;
    let logTitleElement;

    if (progressLog) {
        // Log section exists, clear its content and update title
        logContentElement = progressLog.querySelector('.log-section-content');
        logTitleElement = progressLog.querySelector('.log-section-title');
        if (logTitleElement) logTitleElement.textContent = `${titleText} for Case ${caseIdForTitle}`;
        if (logContentElement) logContentElement.textContent = ''; // Clear previous content
        progressLog.style.display = 'block'; // Ensure it's visible
    } else {
        // Create a new log section
        progressLog = document.createElement('div');
        progressLog.className = 'log-section';

        const logHeader = document.createElement('div');
        logHeader.className = 'log-section-header';

        logTitleElement = document.createElement('div');
        logTitleElement.className = 'log-section-title';
        logTitleElement.textContent = `${titleText} for Case ${caseIdForTitle}`;

        const closeLogBtn = document.createElement('button');
        closeLogBtn.className = 'close-log';
        closeLogBtn.innerHTML = '&times;';
        closeLogBtn.addEventListener('click', function() {
            progressLog.style.display = 'none'; // Hide instead of remove, to keep structure
        });

        logHeader.appendChild(logTitleElement);
        logHeader.appendChild(closeLogBtn);

        logContentElement = document.createElement('div');
        logContentElement.className = 'log-section-content';

        progressLog.appendChild(logHeader);
        progressLog.appendChild(logContentElement);

        const detailsRow = caseCard.querySelector('.case-details-row');
        if (detailsRow && detailsRow.nextSibling) {
            caseCard.insertBefore(progressLog, detailsRow.nextSibling);
        } else {
            caseCard.appendChild(progressLog);
        }
    }
    return { progressLog, logContentElement, logTitleElement };
}

function showHistoricalLogs(caseId, caseCardElement) {
    const { logContentElement } = createOrGetLogSection(caseCardElement, 'Historical Logs', caseId);
    logContentElement.textContent = 'Loading historical logs...\n';
    logContentElement.scrollTop = logContentElement.scrollHeight;

    fetch(`/api/cases/${caseId}/logs`)
        .then(response => response.json())
        .then(data => {
            logContentElement.textContent = ''; // Clear loading message
            if (data.success && data.logs) {
                if (data.logs.length > 0) {
                    data.logs.forEach(logEntry => {
                        const timestamp = new Date(logEntry.timestamp).toLocaleString();
                        logContentElement.textContent += `[${timestamp}] [${logEntry.level}] ${logEntry.message}\n`;
                    });
                } else {
                    logContentElement.textContent = 'No historical logs found for this case.\n';
                }
            } else {
                logContentElement.textContent = `Error loading logs: ${data.error || 'Unknown error'}\n`;
            }
            logContentElement.scrollTop = logContentElement.scrollHeight;
        })
        .catch(error => {
            console.error('Error fetching historical logs:', error);
            logContentElement.textContent += `Failed to fetch logs: ${error.message}\n`;
            logContentElement.scrollTop = logContentElement.scrollHeight;
        });
}
