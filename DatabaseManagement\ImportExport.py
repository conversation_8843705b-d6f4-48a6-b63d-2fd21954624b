import pandas as pd
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from DatabaseManagement.Connections import get_gz_connection
import zlib
import base64
from Alerts.PicturesProcessing.ProcessPicturesShared import is_compressed
import json
import numpy as np
import pyarrow.feather as feather
import pickle
import time
import threading

# Create a global lock for feather file operations
feather_lock = threading.Lock()

def import_excel_to_GZ(file_path, table_name):
    if file_path.endswith('.csv'):
        df = pd.read_csv(file_path)
    else:
        df = pd.read_excel(file_path)
    insert_and_update_df_to_GZ_batch(df, table_name, 'id')

    
# Inserts in batch but does not return the IDs
def insert_and_update_df_to_GZ_batch(df, table_name, key_column, conn=None):
    if len(df) == 0:
        return df
    
    if conn is None:
        conn = get_gz_connection()
    cursor = conn.cursor()
    # gz_cursor.execute(f"TRUNCATE TABLE {table_name}")
    
    print(f"Updating table ({len(df)} records): {table_name}", end="", flush=True)
    
    df = df.copy() # Avoid SettingWithCopyWarning
    df.drop(['status', 'creator', 'create_time', 'updater', 'update_time', 'deleted', 'tenant_id'], axis=1, inplace=True, errors='ignore')
    # df.drop(['status', 'creator', 'create_time', 'updater', 'update_time', 'tenant_id'], axis=1, inplace=True, errors='ignore')

    batch_size = 500
    for start in range(0, len(df), batch_size):
        end = start + batch_size
        batch = df.iloc[start:end].reset_index(drop=True)

        # Convert columns with mixed type but at least one string to string type
        string_column_fixed = []
        for col in batch.columns:
            if batch[col].dtype == 'object' and any(isinstance(x, str) for x in batch[col].dropna()):
                batch[col] = batch[col].apply(lambda x: str(x) if x is not None and not pd.isna(x) else None)
                string_column_fixed.append(col)

        columns_str = ', '.join(batch.columns)

        # Update string to only update if values are different
        update_str = ', '.join([
            f"{col} = CASE WHEN new.{col} <> {table_name}.{col} OR (new.{col} IS NULL AND {table_name}.{col} IS NOT NULL) OR (new.{col} IS NOT NULL AND {table_name}.{col} IS NULL) THEN new.{col} ELSE {table_name}.{col} END"
            for col in batch.columns if col not in [key_column]
        ])

        insert_query = f"""
        INSERT INTO {table_name} ({columns_str})
        VALUES ({', '.join(['%s' for _ in batch.columns])}) AS new
        ON DUPLICATE KEY UPDATE
        {update_str}
        """

        # Prepare values list for batch insert
        values = []
        for _, row in batch.iterrows():
            row_values = [prepare_value(row[col], col, table_name) for col in batch.columns]
            values.append(tuple(row_values))

        # Execute the query with values
        cursor.executemany(insert_query, values)

        print(f" | Batch {end} done", end="", flush=True)

    conn.commit()
    cursor.close()
    conn.close()
    print(f" | ✓ Database update completed! [{len(df)} records]", flush=True)



def insert_df_to_GZ_batch_no_drop(df, table_name, conn=None):
    if len(df) == 0:
        return df
    
    if conn is None:
        conn = get_gz_connection()
    cursor = conn.cursor()
    
    df = df.copy() # Avoid SettingWithCopyWarning

    batch_size = 500
    for start in range(0, len(df), batch_size):
        end = start + batch_size
        batch = df.iloc[start:end].reset_index(drop=True)

        # Convert columns with mixed type but at least one string to string type
        string_column_fixed = []
        for col in batch.columns:
            if batch[col].dtype == 'object' and any(isinstance(x, str) for x in batch[col].dropna()):
                batch[col] = batch[col].apply(lambda x: str(x) if x is not None and not pd.isna(x) else None)
                string_column_fixed.append(col)

        columns_str = ', '.join(batch.columns)

        insert_query = f"""
        INSERT INTO {table_name} ({columns_str})
        VALUES ({', '.join(['%s' for _ in batch.columns])})
        """ #Removed AS new and ON DUPLICATE KEY UPDATE

        # Prepare values list for batch insert
        values = []
        for _, row in batch.iterrows():
            row_values = [prepare_value(row[col], col, table_name) for col in batch.columns]
            values.append(tuple(row_values))

        # Execute the query with values
        cursor.executemany(insert_query, values)

        
        print(f"Batch {start}-{start+batch_size}: done")

    conn.commit()
    cursor.close()
    conn.close()
    print(f"\nDatabase update completed for {table_name}")




def insert_and_update_df_to_GZ_batch_tracking(df, table_name, key_column):
    if len(df) == 0:
        return df
    
    gz_connection = get_gz_connection()
    gz_cursor = gz_connection.cursor()
    df = df.copy() # Avoid SettingWithCopyWarning
    df.drop(['status', 'creator', 'create_time', 'updater', 'update_time', 'deleted', 'tenant_id'], axis=1, inplace=True, errors='ignore')


    total_inserted = 0
    total_updated = 0
    batch_size = 500
    for start in range(0, len(df), batch_size):
        end = start + batch_size
        batch = df.iloc[start:end].reset_index(drop=True)

        # Convert columns with mixed type but at least one string to string type
        string_column_fixed = []
        for col in batch.columns:
            if batch[col].dtype == 'object' and any(isinstance(x, str) for x in batch[col].dropna()):
                batch[col] = batch[col].apply(lambda x: str(x) if x is not None and not pd.isna(x) else None)
                string_column_fixed.append(col)

        # First, check which records exist to determine inserts vs updates
        key_values = [prepare_value(val, key_column, table_name) for val in batch[key_column]]
        check_query = f"SELECT {key_column} FROM {table_name} WHERE {key_column} IN ({','.join(['%s'] * len(key_values))})"
        gz_cursor.execute(check_query, key_values)
        existing_keys = {r[0] for r in gz_cursor.fetchall()}

        columns_str = ', '.join(batch.columns)

        # Modify the update string to only update if values are different
        update_str = ', '.join([
            f"{col} = CASE WHEN new.{col} <> {table_name}.{col} OR (new.{col} IS NULL AND {table_name}.{col} IS NOT NULL) OR (new.{col} IS NOT NULL AND {table_name}.{col} IS NULL) THEN new.{col} ELSE {table_name}.{col} END"
            for col in batch.columns if col not in [key_column]
        ])

        # Replace the MERGE query with MySQL syntax
        insert_query = f"""
        INSERT INTO {table_name} ({columns_str})
        VALUES ({', '.join(['%s' for _ in batch.columns])}) AS new
        ON DUPLICATE KEY UPDATE
        {update_str}
        """

        # Prepare values list for batch insert
        values = []
        for _, row in batch.iterrows():
            row_values = [prepare_value(row[col], col, table_name) for col in batch.columns]
            values.append(tuple(row_values))

        # Execute the query with values
        gz_cursor.executemany(insert_query, values)

        # Count inserts and updates based on key existence
        batch_inserts = sum(1 for val in batch[key_column] if prepare_value(val, key_column, table_name) not in existing_keys)
        batch_updates = sum(1 for val in batch[key_column] if prepare_value(val, key_column, table_name) in existing_keys)
        
        total_inserted += batch_inserts
        total_updated += batch_updates
        print(f"Batch {start}-{start+batch_size}: {batch_inserts} inserted, {batch_updates} updated")

    gz_connection.commit()
    gz_cursor.close()
    gz_connection.close()
    print(f"\nTotal rows processed: {total_inserted + total_updated}, inserted: {total_inserted}, updated: {total_updated}")


# Does not batch (all at once) and returns the IDs
def insert_and_update_df_to_GZ_id(df, table_name, criteria1, criteria2):
    if len(df) == 0:
        return df
    
    gz_connection = get_gz_connection()
    gz_cursor = gz_connection.cursor()
    df = df.copy() # Avoid SettingWithCopyWarning
    df.drop(['id', 'status', 'creator', 'create_time', 'updater', 'update_time', 'deleted', 'tenant_id'], axis=1, inplace=True, errors='ignore')


    # First, find all existing records
    criteria_check_query = f"""
    SELECT {criteria1}, {criteria2}, id
    FROM {table_name}
    WHERE ({criteria1}, {criteria2}) IN ({', '.join(['(%s, %s)'] * len(df))})
    """
    criteria_values = [(row[criteria1], row[criteria2]) for _, row in df.iterrows()]
    criteria_values = [val for pair in criteria_values for val in pair]  # flatten
    gz_cursor.execute(criteria_check_query, criteria_values)
    existing_records = {(str(r[0]), str(r[1])): r[2] for r in gz_cursor.fetchall()}

    # Add ID column to DataFrame
    df['id'] = df.apply(lambda row: existing_records.get((str(row[criteria1]), str(row[criteria2])), None), axis=1)

    # Prepare records for insert/update
    to_upsert = []
    for _, row in df.iterrows():
        values = [prepare_value(row[col], col, table_name) for col in df.columns]
        to_upsert.append(values)

    # Perform UPSERT using ID
    columns_str = ', '.join(df.columns)
    # update_str = ', '.join([f"{col}=VALUES({col})" for col in df.columns if col != 'id'])
    update_str = ', '.join([f"{col}=new.{col}" for col in df.columns if col != 'id'])
    
    upsert_query = f"""
    INSERT INTO {table_name} ({columns_str})
    VALUES ({', '.join(['%s' for _ in range(len(df.columns))])}) AS new
    ON DUPLICATE KEY UPDATE
    {update_str}
    """
    gz_cursor.executemany(upsert_query, to_upsert)
    gz_connection.commit()

    # Get all IDs in one query
    id_query = f"""
    SELECT ID 
    FROM {table_name}
    WHERE ({criteria1}, {criteria2}) IN ({', '.join(['(%s, %s)'] * len(df))})
    """
    gz_cursor.execute(id_query, criteria_values)
    ids = [r[0] for r in gz_cursor.fetchall()]

    gz_cursor.close()
    gz_connection.close()


    # Append the IDs to the DataFrame
    if len(ids) != len(df):
        print(f"Warning: {len(ids)} IDs found, but {len(df)} rows to upsert")
        generate_difference_report(df, table_name, criteria1, criteria2, ids)
    else:
        df['id'] = ids
    return df


def save_df_to_feather(df, table_name):
    """
    Prepares a DataFrame by applying transformations similar to insert_and_update_df_to_GZ_batch
    and saves it to a Feather file.

    Args:
        df (pd.DataFrame): The DataFrame to process and save.
        table_name (str): The name of the table, used for file naming and potentially prepare_value logic.
    """
    print(f"Preparing DataFrame for Feather storage ({len(df)} records): {table_name}...", end="", flush=True)

    df_processed = df.copy() # Work on a copy

    # 2. Apply transformations mimicking insert_and_update_df_to_GZ_batch and prepare_value
    for col in df_processed.columns:
        # Convert mixed-type object columns containing strings to string, (Handles cases where numeric/bool might be mixed with strings)
        if df_processed[col].dtype == 'object' and any(isinstance(x, str) for x in df_processed[col].dropna()):
             df_processed[col] = df_processed[col].apply(lambda x: str(x) if pd.notna(x) else None)

        # Apply prepare_value, passing the value, column name, and table name, This ensures identical processing to the database insert function
        df_processed[col] = df_processed[col].apply(lambda x: prepare_value(x, col, table_name))
    print(" | Processing complete.", end="")

    # Acquire lock before file operations
    with feather_lock:
        # 3. Save to Feather
        file_path = os.path.join(os.getcwd(), "data", "Tables", f"{table_name}.feather")
        try:
            df_processed.to_feather(file_path)
            print(f" | ✓ Saved to {file_path}", flush=True)
        except Exception as e:
            print(f" | ✗ Error saving {table_name} to Feather: {e}", flush=True)


def delete_steps_for_case(case_id):
    gz_connection = get_gz_connection()
    gz_cursor = gz_connection.cursor()
    gz_cursor.execute("DELETE FROM tb_case_steps WHERE case_id = %s", (case_id,))
    gz_connection.commit()
    gz_cursor.close()
    gz_connection.close()

def generate_difference_report(df, table_name, criteria1, criteria2, retrieved_ids):
    import pandas as pd
    from datetime import datetime

    # Establish a new connection for fetching full records
    gz_connection = get_gz_connection()
    gz_cursor = gz_connection.cursor(dictionary=True)

    # Fetch all columns for the specified criteria
    fetch_query = f"""
    SELECT *
    FROM {table_name}
    WHERE ({criteria1}, {criteria2}) IN ({', '.join(['(%s, %s)'] * len(df))})
    """
    criteria_values = [(row[criteria1], row[criteria2]) for _, row in df.iterrows()]
    criteria_values = [val for pair in criteria_values for val in pair]  # flatten
    gz_cursor.execute(fetch_query, criteria_values)
    db_records = gz_cursor.fetchall()

    gz_cursor.close()
    gz_connection.close()

    # Convert fetched records to DataFrame
    db_df = pd.DataFrame(db_records)
    db_df[criteria1] = db_df[criteria1].astype(str)
    db_df[criteria2] = db_df[criteria2].astype(str)
    df[criteria1] = df[criteria1].astype(str)
    df[criteria2] = df[criteria2].astype(str)

    # Merge DataFrames on criteria to identify discrepancies
    merged_df = pd.merge(df, db_df, on=[criteria1, criteria2], how='left', suffixes=('_df', '_db'))

    # Identify rows with missing IDs
    discrepancies_missing_id = merged_df[(merged_df['id_db'].isnull())]
    # Identify rows with multiple IDs
    discrepancies_multiple_id = merged_df[(merged_df.duplicated(subset=[criteria1, criteria2], keep=False))]
    discrepancies_multiple_id.sort_values(by=[criteria1, criteria2], inplace=True)

    # Prepare the report
    if not discrepancies_missing_id.empty:
        report_filename = f"difference_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}_missing_id.csv"
        discrepancies_missing_id.to_csv(report_filename, index=False)
        print(f"Difference report generated: {report_filename}")
        for _, row in discrepancies_missing_id.head(10).iterrows():
            print()
            print(f"Missing ID for {row[criteria1]} {row[criteria2]}:")
            print(row)
    if not discrepancies_multiple_id.empty:
        report_filename = f"difference_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}_multiple_id.csv"
        discrepancies_multiple_id.to_csv(report_filename, index=False)
        print(f"Difference report generated: {report_filename}")
        for _, row in discrepancies_multiple_id.head(10).iterrows():
            print()
            print(f"Multiple IDs for {row[criteria1]} {row[criteria2]}:")
            print(row)
    else:
        print("No discrepancies found between DataFrame and database records.")


def get_last_timestamp_from_GZ(table_name, field_name):
    gz_connection = get_gz_connection()
    gz_cursor = gz_connection.cursor()
    gz_cursor.execute(f"SELECT MAX({field_name}) FROM {table_name}")
    last_timestamp = gz_cursor.fetchone()[0]
    gz_cursor.close()
    gz_connection.close()
    return last_timestamp


def export_excel_from_GZ(file_path, table_name):
    df = get_table_from_GZ(table_name)
    df.to_excel(file_path, index=False)


def get_table_from_GZ(table_name, force_refresh=True, chunk_size=1000, where_clause: str = None):
    start_time = time.time()
    tables_folder = os.path.join(os.getcwd(), "data", "Tables")
    os.makedirs(tables_folder, exist_ok=True)
    file_path = os.path.join(tables_folder, f"{table_name}.feather")

    # If where_clause is provided and is not empty, skip feather cache reading
    if not (where_clause and where_clause.strip()):
        if os.path.exists(file_path) and not force_refresh:
            try:
                # Check file modification time if needed for more robust caching
                df = feather.read_feather(file_path)
                if table_name == "tb_case" and 'images' in df.columns:
                    df["images"] = df["images"].apply(lambda x: json.loads(zlib.decompress(base64.b64decode(x)).decode('utf-8')) if pd.notna(x) and isinstance(x, str) else x)
                print(f"!!! {table_name} loaded from feather file in {time.time() - start_time:.2f} seconds")
                return df
            except Exception as e:
                print(f"Warning: Error reading feather file {file_path}, refreshing data. Error: {e}")
    else:
        # Optional: Log that feather read is skipped
        print(f"Skipping feather cache read for {table_name} due to where_clause.")


    all_rows = []
    columns = []
    df = pd.DataFrame() # Initialize df to ensure it's always defined

    try:
        with get_gz_connection() as gz_connection:
            with gz_connection.cursor() as gz_cursor:
                print(f"Fetching '{table_name}' from GZ: ", end="", flush=True)
                # Read data from US database
                if where_clause and where_clause.strip():
                    query = f"SELECT * FROM {table_name} WHERE {where_clause}"
                    # print(f" with WHERE clause: {where_clause}", end="", flush=True) # Optional logging
                else:
                    query = f"SELECT * FROM {table_name}"
                gz_cursor.execute(query)
                columns = [desc[0] for desc in gz_cursor.description] # Get columns after execute

                fetched_count = 0
                while True:
                    fetch_start_time = time.time()
                    # Fetch data in chunks
                    chunk = gz_cursor.fetchmany(chunk_size)
                    if not chunk:
                        break # No more data

                    all_rows.extend(chunk) # More efficient than appending lists
                    fetched_count += len(chunk)
                    fetch_duration = time.time() - fetch_start_time
                    total_duration = time.time() - start_time
                    print(f" | {fetched_count/1000:.0f}k ({fetch_duration:.1f}s)", end="", flush=True)

        # Convert list of rows to DataFrame *after* closing the connection
        if not all_rows:
             print(f"Warning: No data fetched for table {table_name}.")
             df = pd.DataFrame([], columns=columns) # Create empty DataFrame with correct columns
        else:
            df = pd.DataFrame(all_rows, columns=columns)
            all_rows = [] # Free memory

        print(f" | DONE ({time.time() - start_time:.2f}s)")

        # Save to feather file only if no where_clause was used (or if it was empty/None)
        if not (where_clause and where_clause.strip()):
            with feather_lock: # Use the global lock for feather file operations
                feather.write_feather(df, file_path)
        else:
            # Optional: Log that feather write is skipped
            print(f" | Skipping feather write for {table_name} due to where_clause.", end="")

        # Automatically decompress and parse the 'images' column for tb_case table
        if table_name == "tb_case" and 'images' in df.columns:
            df['images'] = df['images'].apply(lambda x: json.loads(zlib.decompress(base64.b64decode(x)).decode('utf-8')) if pd.notna(x) and isinstance(x, str) else x)

    except Exception as e:
        print(f"An error occurred while fetching or processing table {table_name}: {e}")

    return df

def get_table_from_GZ_with_connection(gz_connection, table_name):
    with gz_connection.cursor() as gz_cursor:
        # Read data from US database
        query = f"SELECT * FROM {table_name}"
        gz_cursor.execute(query)
        
        # Fetch all data at once
        data = gz_cursor.fetchall()
        
        # Convert to DataFrame first
        df = pd.DataFrame(data, columns=[desc[0] for desc in gz_cursor.description])
    
    # Automatically decompress and parse the 'images' column for tb_case table
    if table_name == "tb_case" and 'images' in df.columns:
        try:
            df['images'] = df['images'].apply(
                lambda x: json.loads(zlib.decompress(base64.b64decode(x)).decode('utf-8')) 
                if pd.notna(x) and isinstance(x, str) else x
            )
            print(f"Automatically decompressed and parsed 'images' column for {table_name} (with connection)")
        except Exception as e:
            print(f"Error decompressing 'images' column for {table_name}: {e}")
    
    return df


def get_subscibed_cases():
    with get_gz_connection() as gz_connection:
        with gz_connection.cursor() as gz_cursor:
            # Get all records from cases where: 1. the case_id is in the case_id field in the table tb_user_case and 2. the field class_code == "Open"
            # First get the cases
            query = """
                SELECT c.* 
                FROM tb_case c
                INNER JOIN tb_user_case uc ON c.id = uc.case_id 
                WHERE c.class_code = 'Open'
            """
            df_subscribed_cases = pd.read_sql_query(query, gz_connection)
    
    return df_subscribed_cases

def prepare_value(value, column_name, table_name):
    if pd.isna(value):
        if column_name == 'images':
            print(f"\033[91m!!! Image is None\033[0m")
        return None
    elif isinstance(value, pd.Timestamp):
        return value.to_pydatetime()
    elif isinstance(value, dict) and column_name == 'images':
        jsonstr =  json.dumps(value)
        if len(jsonstr) < 50 and "check" not in table_name:
            print(f"!!! \033[91m{jsonstr} is too short: {len(jsonstr)}\033[0m")
        compressed = zlib.compress(jsonstr.encode('utf-8')) # The .encode('utf-8') part is crucial; zlib works on bytes, not strings. 
        return base64.b64encode(compressed).decode('utf-8') #  Base64 converts binary data into a text format that can be safely stored in databases.  The final .decode('utf-8') converts the Base64 encoded bytes back into a string.
    elif isinstance(value, dict):
        return json.dumps(value)
    elif isinstance(value, str) and column_name == 'images' and not is_compressed(value):
        if len(value) < 50 and "check" not in table_name:
            print(f"!!! \033[91m{value} is too short: {len(value)}\033[0m")
        compressed = zlib.compress(value.encode('utf-8'))
        return base64.b64encode(compressed).decode('utf-8')
    elif 'int' in str(type(value)):  # Handles numpy.int64, numpy.int32, etc.
        return int(value)
    return value


def update_lexis_stats(date, case_id, pacer_refresh_count, pacer_file_download_count, total_file_count):
    gz_connection = get_gz_connection()
    gz_cursor = gz_connection.cursor()

    # get any record for date in lexis table
    gz_cursor.execute("SELECT * FROM lexis_stats WHERE date = %s", (date,))
    record = gz_cursor.fetchone()
    if record:
        # update the record: add pacer_refresh_count to refresh_count and pacer_file_download_count to file_count
        gz_cursor.execute("UPDATE lexis_stats SET pacer_refresh = pacer_refresh + %s, pacer_file = pacer_file + %s, total_file = total_file + %s WHERE date = %s", (int(pacer_refresh_count), int(pacer_file_download_count), int(total_file_count), date))
    else:
        # insert the record
        gz_cursor.execute("INSERT INTO lexis_stats (date, pacer_refresh, pacer_file, total_file) VALUES (%s, %s, %s, %s)", (int(date), int(pacer_refresh_count), int(pacer_file_download_count), int(total_file_count)))

    gz_connection.commit()
    gz_cursor.close()
    gz_connection.close()


def migrate_from_old_database_to_miniapp():
    df = get_table_from_GZ("cases")
    df.drop(columns=['link_snippet', 'link'], inplace=True)
    df.rename(columns={'assignedto': 'assigned_to', 'nosdescription': 'nos_description', 'demandamount': 'demand_amount', 'classcode': 'class_code'}, inplace=True)
    df.rename(columns={'plaintiffnames': 'plaintiff_names', 'plaintifflawyers': 'plaintiff_lawyers', 'defendentnames': 'defendant_names', 'defendentlawyers': 'defendant_lawyers'}, inplace=True)
    df.rename(columns={'filesstatus': 'file_status'}, inplace=True)
    df.rename(columns={'created_at': 'create_time'}, inplace=True)
    df.rename(columns={'updated_at': 'update_time'}, inplace=True)
    import numpy as np
    df['id'] = df['id'].astype(np.int64)
    df['date_filed'] = pd.to_datetime(df['date_filed']).dt.date
    df['closed'] = pd.to_datetime(df['closed']).dt.date
    insert_and_update_df_to_GZ_batch(df, "tb_case", "id")

    df = get_table_from_GZ("plaintiff")
    df.rename(columns={'created_at': 'create_time', 'updated_at': 'update_time'}, inplace=True)
    insert_and_update_df_to_GZ_batch(df, "tb_plaintiff", "id")

    df = get_table_from_GZ("cases_steps")
    df.rename(columns={'created_at': 'create_time', 'updated_at': 'update_time'}, inplace=True)
    df.rename(columns={'caseid': 'case_id'}, inplace=True)
    df['step_date_filed'] = pd.to_datetime(df['step_date_filed']).dt.date
    df.drop(columns=['source'], inplace=True)
    df.dropna(subset=['case_id'], inplace=True)
    insert_and_update_df_to_GZ_batch(df, "tb_case_steps", "id")


    new_row = df.iloc[-1].copy()
    new_row = new_row.to_frame().T
    print(new_row)
    new_row.drop(columns=['id'], inplace=True)
    new_row['docket'] = "testDocket2"
    new_row['court'] = "testCourt2"
    # insert_and_update_df_to_GZ_batch(new_row, "tb_case", "id")


    insert_and_update_df_to_GZ_id(new_row, "tb_case", "docket", "court")
    print(new_row)
    
    # import_excel_to_GZ("D:\\Win10User\\Downloads\\CASES_STEPS_202410272013.csv", "tb_case_steps")
    print(df)

    # To do: add created at automatically, add updated at automatically,



if __name__ == "__main__":
    import time
    start_time = time.time()
    df = get_table_from_GZ("tb_case", force_refresh=True)
    end_time = time.time()
    print(f"Time taken for tb_case to fetch data: {end_time - start_time:.2f} seconds")
    start_time = time.time()
    df = get_table_from_GZ("tb_case_steps")
    end_time = time.time()
    print(f"Time taken for tb_case_steps to fetch data: {end_time - start_time:.2f} seconds")
    start_time = time.time()
    df = get_table_from_GZ("tb_plaintiff")
    end_time = time.time()
    print(f"Time taken for tb_plaintiff to fetch data: {end_time - start_time:.2f} seconds")
    start_time = time.time()
    df = get_table_from_GZ("tb_case_check")
    end_time = time.time()
    print(f"Time taken for tb_case_check to fetch data: {end_time - start_time:.2f} seconds")
    start_time = time.time()
    df = get_table_from_GZ("tb_case_check_result")
    end_time = time.time()
    print(f"Time taken for tb_case_check_result to fetch data: {end_time - start_time:.2f} seconds")


    # df = df[df['docket'].str.contains('08739')]
    # print(df)
    # check_image_problem_during_migration()

