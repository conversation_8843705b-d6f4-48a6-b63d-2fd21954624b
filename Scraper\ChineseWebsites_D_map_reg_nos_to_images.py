#nofloqa
import asyncio
import os
import json
import re # For JSON extraction
from typing import List, Dict, Optional, Any

from AI.GC_VertexAI import vertex_genai_multi_async
import Common.Constants as Constants
from logdata import log_message
from langfuse.decorators import observe


def _extract_json_from_llm_response(response_text: str) -> Optional[List[Dict[str, str]]]:
    """
    Extracts a JSON list of objects from the LLM's response string.
    Tries to handle markdown code blocks or direct JSON.
    """
    if not response_text:
        log_message("    D_Map_LLM_Parse: LLM response was empty.", level="WARNING")
        return None
    
    response_text = response_text.strip()
    
    match = re.search(r"```json\s*([\s\S]*?)\s*```", response_text, re.IGNORECASE)
    if match:
        json_str = match.group(1).strip()
    elif response_text.startswith("[") and response_text.endswith("]"):
        json_str = response_text
    else:
        start_index = response_text.find('[')
        end_index = response_text.rfind(']')
        if start_index != -1 and end_index != -1 and end_index > start_index:
            json_str = response_text[start_index : end_index+1]
        else:
            log_message(f"    D_Map_LLM_Parse: Could not find JSON list structure. Response snippet: {response_text[:200]}", level="WARNING")
            return None

    try:
        parsed_json = json.loads(json_str)
        if isinstance(parsed_json, list):
            if not parsed_json: # Empty list is valid
                 return []
            if all(isinstance(item, dict) and "image_identifier" in item and "matched_usco_reg_no" in item for item in parsed_json):
                return parsed_json
            else:
                log_message(f"    D_Map_LLM_Parse: Parsed JSON list items do not have expected keys. Snippet: {str(parsed_json)[:200]}", level="WARNING")
                return None
        else:
            log_message(f"    D_Map_LLM_Parse: Parsed JSON is not a list. Type: {type(parsed_json)}. Snippet: {json_str[:200]}", level="WARNING")
            return None
    except json.JSONDecodeError as e:
        log_message(f"    D_Map_LLM_Parse: Failed to decode JSON: {e}. JSON string snippet: {json_str[:200]}", level="WARNING")
        return None

@observe()
async def D_map_reg_nos_to_images_async(
    d_step_input_items: List[Dict[str, Any]], # Combined list with all necessary info
    master_copyright_details_from_usco: Dict[str, Dict[str, str]], # This is {std_reg_no: {title, claimant, reg_no}}
    docket: str
) -> List[Dict[str, Any]]:
    """
    Maps final processed copyright images to the best matching USCO registration number using an LLM.

    Args:
        d_step_input_items: A list of dictionaries. Each dictionary contains all context
                              for an image processed by C-step, including its
                              `processed_image_path_from_C` (path to send to LLM),
                              `reg_no` (original C-step input identifier),
                              `candidate_usco_reg_nos`, `img_url` (original from B), etc.
        master_copyright_details_from_usco: Dict of {std_reg_no: {title, claimant, reg_no}}.
        docket: The case docket number for context.
    Returns:
        A list of dictionaries, each containing details for a processed image:
        {"final_image_path": str, "matched_usco_reg_no": Optional[str], 
         "source_site": str, "original_image_url": str, "image_identifier_C": str}
    """
    processed_images_details_list: List[Dict[str, Any]] = []
    llm_input_items_details = [] # Stores details for each image to be processed by LLM
        
    # Check if master_copyright_details_from_usco is empty
    if not master_copyright_details_from_usco:
        log_message("    D_Map_Prep: No USCO registration details available in master_copyright_details_from_usco. Cannot perform LLM mapping.", level="WARNING")
        # Populate all images from C with None match
        for d_item_no_usco in d_step_input_items:
            processed_images_details_list.append({
                "final_image_path": d_item_no_usco["processed_image_path_from_C"],
                "matched_usco_reg_no": None,
                "source_site": d_item_no_usco.get('source_site', 'N/A'),
                "original_image_url": d_item_no_usco.get('img_url', 'N/A'), # This is original img_url from B
                "image_identifier_C": d_item_no_usco["reg_no"] # This is original C-step input identifier
            })
        return processed_images_details_list

    # Prepare data for each image
    for idx, d_input_item in enumerate(d_step_input_items):
        sequential_llm_id = f"Image_{idx + 1}"
        # This is the original C-step input identifier (e.g., "multi_1_part_1", "VA123")
        original_image_identifier_C = d_input_item["reg_no"] 
        # This is the path to the image file processed by C, to be shown to the LLM
        path_to_llm_image = d_input_item["processed_image_path_from_C"] 

        if not os.path.exists(path_to_llm_image):
            log_message(f"    D_Map_Prep: Image path {path_to_llm_image} (ID: {original_image_identifier_C}) does not exist. Skipping LLM mapping.", level="WARNING")
            # Still add to processed_images_details_list as it was an image from C that failed at this stage
            processed_images_details_list.append({
                "final_image_path": path_to_llm_image, # Path might be invalid, but good to record
                "matched_usco_reg_no": None,
                "source_site": d_input_item.get('source_site', 'N/A'),
                "original_image_url": d_input_item.get('img_url', 'N/A'), # Original img_url from B
                "image_identifier_C": original_image_identifier_C
            })
            continue

        # Get suggested candidates from B-step (if any)
        candidate_usco_reg_nos_str_list = d_input_item.get("candidate_usco_reg_nos", [])

        # This is the original image URL from the B-step, associated with the C-step input
        original_img_url_from_B = d_input_item.get('img_url', 'N/A') 
        original_img_filename_for_prompt = os.path.basename(original_img_url_from_B) if original_img_url_from_B != 'N/A' else 'N/A'

        llm_input_items_details.append({
            "sequential_llm_id": sequential_llm_id,
            "original_image_identifier_C": original_image_identifier_C, # For final output mapping
            "final_image_path": path_to_llm_image, # Path to image LLM will see
            "original_img_url_for_prompt": original_img_filename_for_prompt,
            "source_site_for_output": d_input_item.get('source_site', 'N/A'), # For final output list
            "original_img_url_for_output": original_img_url_from_B, # For final output list
            "suggested_candidate_reg_nos": candidate_usco_reg_nos_str_list # List of strings
        })

    if not llm_input_items_details:
        log_message("    D_Map: No images eligible for LLM mapping after preparation.", level="INFO")
        return processed_images_details_list # Return list which might contain items skipped before LLM prep

    log_message(f"    D_Map: Processing {len(llm_input_items_details)} images for LLM mapping.", level="INFO")

    # The set of all valid USCO reg nos for validation of LLM response
    valid_usco_reg_nos_for_validation = set(master_copyright_details_from_usco.keys())

    # --- Construct LLM Prompt for all images ---
    llm_api_input_parts = []

    # Initial text part (instructions and USCO list)
    initial_prompt_text_parts = [
        f"You are an expert copyright analyst. You will be provided with a series of images and a comprehensive list of USCO copyright registration details relevant to case {docket}.\n"
        "For each image, I will give you an 'Image Identifier', its original filename from the website, and potentially a list of 'Suggested Candidate Reg Nos'.\n"
        "Your task is to analyze each image and select the single best matching USCO Registration Number from the 'Overall USCO Details List' provided below.\n"
        "If 'Suggested Candidate Reg Nos' are provided for an image, consider them, but you are not limited to them. Your final choice must come from the 'Overall USCO Details List'.\n"
        "If no registration number from the 'Overall USCO Details List' is a good match for an image, or if you are uncertain, you MUST return the string \"None\" for that image.\n\n"
        "Output your response as a single JSON list of objects. Each object in the list must have two keys:\n"
        "1. \"image_identifier\": The 'Image Identifier' I provided for that image (e.g., \"Image_1\").\n"
        "2. \"matched_usco_reg_no\": The selected USCO registration number (e.g., \"VA0001234567\") from the 'Overall USCO Details List', or the exact string \"None\".\n\n"
        "Ensure every image for which an 'Image Identifier' was provided in this prompt is represented in your JSON output.\n"
        "Do not include any text before or after the JSON list.\n\n"
        "--- Overall USCO Details List (Candidates for all images) ---\n"
    ]

    for cand_idx, usco_detail in enumerate(master_copyright_details_from_usco.values()):
        initial_prompt_text_parts.append(
            f"  USCO Detail {cand_idx + 1}: Reg No: {usco_detail.get('registration_number', 'N/A')}, "
            f"Title: {usco_detail.get('title', 'N/A')}, Claimant: {usco_detail.get('names', 'N/A')}\n"
        )
    initial_prompt_text_parts.append("\n--- Images for Analysis ---\n\n")
    llm_api_input_parts.append(("text", "".join(initial_prompt_text_parts)))

    # Interleave image-specific text and image data
    for img_data in llm_input_items_details:
        image_specific_text_parts = [
            f"--- {img_data['sequential_llm_id']} ---\n",
            f"Image Identifier: {img_data['sequential_llm_id']}\n",
            f"Original Filename from Website: {img_data['original_img_url_for_prompt']}\n"
        ]
        if img_data['suggested_candidate_reg_nos']:
            image_specific_text_parts.append(f"Suggested Candidate Reg Nos: {', '.join(img_data['suggested_candidate_reg_nos'])}\n")
        else:
            image_specific_text_parts.append("Suggested Candidate Reg Nos: None provided.\n")
        image_specific_text_parts.append("---\n\n")
        
        llm_api_input_parts.append(("text", "".join(image_specific_text_parts)))
        llm_api_input_parts.append(("image_path", img_data["final_image_path"])) # Full path for API
    
    llm_api_input_parts.append(("text", "Remember to provide your entire response as a single JSON list."))
    # --- End LLM Prompt Construction ---

    try:
        llm_response_str = await vertex_genai_multi_async(llm_api_input_parts, model_name=Constants.IMAGE_MODEL_FREE_LIMITED, useVertexAI=Constants.IMAGE_MODEL_FREE_LIMITED_VERTEX)
        
        if not llm_response_str:
            log_message("    D_Map: LLM returned an empty response. Marking all images as 'None'.", level="WARNING")
            for item_data in llm_input_items_details: # Iterate over the prepared items
                processed_images_details_list.append({
                    "final_image_path": item_data["final_image_path"],
                    "matched_usco_reg_no": None,
                    "source_site": item_data["source_site_for_output"],
                    "original_image_url": item_data["original_img_url_for_output"],
                    "image_identifier_C": item_data["original_image_identifier_C"]
                })
            return processed_images_details_list

        parsed_llm_results = _extract_json_from_llm_response(llm_response_str)

        if parsed_llm_results is None:
            log_message(f"    D_Map: Failed to parse JSON response from LLM. LLM Raw: '{llm_response_str[:300]}...'. Marking all images as 'None'.", level="WARNING")
            for item_data in llm_input_items_details:
                 processed_images_details_list.append({
                    "final_image_path": item_data["final_image_path"],
                    "matched_usco_reg_no": None,
                    "source_site": item_data["source_site_for_output"],
                    "original_image_url": item_data["original_img_url_for_output"],
                    "image_identifier_C": item_data["original_image_identifier_C"]
                })
            return processed_images_details_list

        if not parsed_llm_results and llm_input_items_details: # LLM returned empty list []
             log_message(f"    D_Map: LLM returned an empty JSON list for a non-empty set of {len(llm_input_items_details)} images. All will be marked as 'None'.", level="WARNING")
        
        # Create a map from the LLM's response for easy lookup
        llm_response_map_by_sequential_id = {
            item["image_identifier"]: item["matched_usco_reg_no"] 
            for item in parsed_llm_results 
            if isinstance(item, dict) and "image_identifier" in item and "matched_usco_reg_no" in item
        }

        # Iterate through the original list of items we prepared for the LLM
        for item_data in llm_input_items_details:
            sequential_id = item_data["sequential_llm_id"]
            llm_matched_reg_no_str = llm_response_map_by_sequential_id.get(sequential_id)
            final_usco_match_for_this_image = None

            if llm_matched_reg_no_str is None:
                log_message(f"    D_Map_Result: Sequential LLM ID '{sequential_id}' (Original ID: {item_data['original_image_identifier_C']}) not found in LLM JSON response. Image: {os.path.basename(item_data['final_image_path'])}", level="WARNING")
            elif isinstance(llm_matched_reg_no_str, str) and llm_matched_reg_no_str.lower().strip() in ["none", "null", ""]:
                log_message(f"    D_Map_Result: LLM indicated no match for {os.path.basename(item_data['final_image_path'])} (Sequential ID: {sequential_id}, Original ID: {item_data['original_image_identifier_C']}).", level="INFO")
            elif llm_matched_reg_no_str in valid_usco_reg_nos_for_validation:
                final_usco_match_for_this_image = llm_matched_reg_no_str
                log_message(f"    D_Map_Result: LLM matched {os.path.basename(item_data['final_image_path'])} (Sequential ID: {sequential_id}, Original ID: {item_data['original_image_identifier_C']}) to {llm_matched_reg_no_str}", level="INFO")
            else:
                log_message(f"    D_Map_Result: LLM returned an invalid/unexpected reg_no '{llm_matched_reg_no_str}' for {os.path.basename(item_data['final_image_path'])} (Sequential ID: {sequential_id}, Original ID: {item_data['original_image_identifier_C']}). Not in master USCO list. Assigning None.", level="WARNING")
            
            processed_images_details_list.append({
                "final_image_path": item_data["final_image_path"], # Full path for output
                "matched_usco_reg_no": final_usco_match_for_this_image,
                "source_site": item_data["source_site_for_output"],
                "original_image_url": item_data["original_img_url_for_output"],
                "image_identifier_C": item_data["original_image_identifier_C"]
            })
        
    except Exception as e:
        log_message(f"    D_Map: Error during LLM call or processing: {e}", level="ERROR")
        # Add all images from this failed call to the list with None for matched_usco_reg_no
        for item_data_on_error in llm_input_items_details:
            processed_images_details_list.append({
                "final_image_path": item_data_on_error["final_image_path"],
                "matched_usco_reg_no": None,
                "source_site": item_data_on_error["source_site_for_output"],
                "original_image_url": item_data_on_error["original_img_url_for_output"],
                "image_identifier_C": item_data_on_error["original_image_identifier_C"]
            })

    return processed_images_details_list