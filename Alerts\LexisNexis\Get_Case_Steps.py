from langfuse.decorators import observe
import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
import traceback
import re
from Common.Constants import download_patterns, no_download_patterns # Removed: from .Download_File import get_step_file
from logdata import log_message
import pandas as pd
from decimal import Decimal, ROUND_HALF_UP

# Constants for priority
PRIORITY_COMPLAINT = 1
PRIORITY_AMENDED_COMPLAINT = 2
PRIORITY_TRO = 3
PRIORITY_PRELIM_INJUNCTION = 4
PRIORITY_FINAL_JUDGMENT = 5
PRIORITY_ANSWER = 6
PRIORITY_OTHER = 20 # Lower priority for general downloadable items
 
# Mapping from priority constants to names
PRIORITY_NAMES = {
    PRIORITY_COMPLAINT: "PRIORITY_COMPLAINT",
    PRIORITY_AMENDED_COMPLAINT: "PRIORITY_AMENDED_COMPLAINT",
    PRIORITY_TRO: "PRIORITY_TRO",
    PRIORITY_PRELIM_INJUNCTION: "PRIORITY_PRELIM_INJUNCTION",
    PRIORITY_FINAL_JUDGMENT: "PRIORITY_FINAL_JUDGMENT",
    PRIORITY_ANSWER: "PRIORITY_ANSWER",
    PRIORITY_OTHER: "PRIORITY_OTHER",
}
 
def assign_document_priorities(steps_df, docket_number="N/A", court="N/A"):
    """
    Assigns download priorities to steps in the DataFrame.
    Modifies the 'priority' column in steps_df in place.

    Args:
        steps_df: Pandas DataFrame containing case steps. Must include 'row_index', 
                  'proceeding_text', 'step_date_filed', and 'is_downloadable' columns.
        docket_number: Docket number for logging.
        court: Court identifier for logging.

    Returns:
        The modified DataFrame with a 'priority' column.
    """
    if 'priority' not in steps_df.columns:
        steps_df['priority'] = PRIORITY_OTHER
    else:
        steps_df['priority'] = steps_df['priority'].fillna(PRIORITY_OTHER).astype(int)

    # Helper function to apply a specific priority rule
    def _apply_priority_rule(df, start_with, include_regex, exclude_regex, rule_priority):
        item_date = None
        # Iterate using index to allow modification with .loc
        for idx in df.index:
            try:
                # Early exit if date changed after finding main item for this rule
                if item_date is not None and df.loc[idx, 'step_date_filed'] != item_date:
                    break 
                
                # Skip if not downloadable
                if not df.loc[idx, 'is_downloadable']:
                    continue

                step_description = str(df.loc[idx, 'proceeding_text']).lower()
                current_doc_date = df.loc[idx, 'step_date_filed']

                # Try to find the main document for this rule
                if item_date is None:
                    if step_description.startswith(start_with) and \
                       re.search(include_regex, step_description) and \
                       not (exclude_regex and re.search(exclude_regex, step_description)):
                        
                        # Apply priority if it's better than current
                        if rule_priority < df.loc[idx, 'priority']:
                            df.loc[idx, 'priority'] = rule_priority
                        item_date = current_doc_date
                
                # If main document for this rule was found, look for its exhibits on the same date
                elif item_date == current_doc_date: # item_date is not None here
                    if "exhibit" in step_description and \
                       not (exclude_regex and re.search(exclude_regex, step_description)):
                        
                        # Apply priority if it's better than current
                        if rule_priority < df.loc[idx, 'priority']:
                            df.loc[idx, 'priority'] = rule_priority
            except Exception as e:
                log_message(f"Error processing row index {idx} in _apply_priority_rule for {docket_number}: {e}")
                continue

    # Apply rules. Order can matter if priorities are the same;
    # however, the `rule_priority < df.loc[idx, 'priority']` check ensures
    # that a more important (lower number) priority always wins.
    # 1. Complaint
    _apply_priority_rule(steps_df, start_with="complaint", include_regex="", exclude_regex="amended complaint|temporary restraining order|\bmotion|memorandum|declaration|\bao 120", rule_priority=PRIORITY_COMPLAINT)
    # 2. Amended Complaint 
    _apply_priority_rule(steps_df, start_with="amended complaint", include_regex="", exclude_regex="temporary restraining order|memorandum|declaration|\bao 120", rule_priority=PRIORITY_AMENDED_COMPLAINT)
    # 3. TRO
    _apply_priority_rule(steps_df, start_with="memorandum", include_regex="temporary restraining order", exclude_regex="preliminary injunction|\bao 120", rule_priority=PRIORITY_TRO)
    _apply_priority_rule(steps_df, start_with="motion", include_regex="temporary restraining order", exclude_regex="preliminary injunction|\bao 120", rule_priority=PRIORITY_TRO)
    # 4. Preliminary Injunction
    _apply_priority_rule(steps_df, start_with="memorandum", include_regex="preliminary injunction", exclude_regex="\bao 120", rule_priority=PRIORITY_PRELIM_INJUNCTION)
    _apply_priority_rule(steps_df, start_with="motion", include_regex="preliminary injunction", exclude_regex="\bao 120", rule_priority=PRIORITY_PRELIM_INJUNCTION)
    # 5. Final Judgment
    _apply_priority_rule(steps_df, start_with="final", include_regex="\bjudgment", exclude_regex="", rule_priority=PRIORITY_FINAL_JUDGMENT)
    _apply_priority_rule(steps_df, start_with="default", include_regex="\bjudgment", exclude_regex="", rule_priority=PRIORITY_FINAL_JUDGMENT)
    # 6. Answer
    _apply_priority_rule(steps_df, start_with="answer", include_regex="\bto.*complaint", exclude_regex="", rule_priority=PRIORITY_ANSWER)
    _apply_priority_rule(steps_df, start_with="response", include_regex="\bto.*temporary restraining order", exclude_regex="", rule_priority=PRIORITY_ANSWER)
    _apply_priority_rule(steps_df, start_with="response", include_regex="\bto.*preliminary injunction", exclude_regex="", rule_priority=PRIORITY_ANSWER)

    priority_counts = steps_df[steps_df['priority'] != PRIORITY_OTHER]['priority'].value_counts().to_dict()
    priority_names_counts = {PRIORITY_NAMES.get(p, str(p)): c for p, c in priority_counts.items()}
    if priority_names_counts:
        log_message(f"Assigned priorities for {docket_number} from {court}. Counts: {priority_names_counts}")
    else:
        log_message(f"No specific priorities assigned for {docket_number} from {court} beyond PRIORITY_OTHER.")
        
    return steps_df


@observe()
def get_case_steps(driver, docket_number, court, options):
    """
    Parses the case steps table, identifies potential download steps with priorities,
    and returns a DataFrame with step details including priority.
    Does NOT perform any downloads.

    Args:
        driver: Selenium WebDriver instance.
        docket_number: The docket number of the case.
        court: The court identifier.

    Returns:
        A tuple: (pandas.DataFrame, bool)
        - DataFrame containing all parsed steps ('row_index', 'step_nb_raw', 'step_nb', 'step_date_filed',
          'proceeding_text', 'docket', 'court', 'is_downloadable', 'priority', 'priority_name',
          'proceeding_text_cn', 'files_number', 'files_downloaded', 'files_failed').
        - Boolean status indicating if parsing was successful. Returns (empty_df, False) on failure.
    """

    try:
        # Wait for the table and validate
        try:
            table = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.CLASS_NAME, "SS_DataTable")))
        except TimeoutException:
            log_message(f"!No steps table found for {docket_number} from {court}.")
            return pd.DataFrame(), True # Return empty DataFrame on failure

        rows = table.find_elements(By.TAG_NAME, 'tr')
        if len(rows) <= 1:
            log_message(f"No data rows found in table for {docket_number} from {court}.")
            return pd.DataFrame(), False # Return empty DataFrame on failure

        header_row = rows[0]
        if len(header_row.find_elements(By.TAG_NAME, 'th')) != 6:
            log_message(f"Table does not have 6 columns for {docket_number} from {court}. Parsing failed.")
            return pd.DataFrame(), False

        # Single pass to extract all data
        raw_steps_data = []
        # Iterate through rows and extract data
        for row_nb, tr in enumerate(rows[1:], start=1): # Start row_nb from 1 (this is the 1-based index)
            try:
                cells_elements = tr.find_elements(By.TAG_NAME, 'td')
                # Expecting 6 columns: Checkbox, Availability,  Step #, Date Filed, Proceeding Text, Source
                if len(cells_elements) < 6:
                     log_message(f"Skipping row {row_nb} for {docket_number}: Not enough cells ({len(cells_elements)}).")
                     continue

                # Extract data (Indices based on observed structure)
                availability_text = cells_elements[1].text.strip()
                step_nb_raw = cells_elements[2].text.strip() # Keep raw step number text
                date_filed_str = cells_elements[3].text.strip()
                proceeding_text_element = cells_elements[4] # WebElement for Proceeding Text
                proceeding_text = proceeding_text_element.text.strip() # Text content
 
                is_downloadable = len(availability_text) > 0 and availability_text != "N/A"
 
                # Attempt to count files (links in proceeding text column + 1 for main doc)
                files_number = 0
                if is_downloadable:
                    try:
                        # Count links within the proceeding text cell
                        links = proceeding_text_element.find_elements(By.TAG_NAME, 'a')
                        # Add 1 for the main document itself if links are present or assume 1 if no links but downloadable
                        files_number = len(links) + 1
                    except Exception as link_ex:
                        log_message(f"Warning: Could not count links in proceeding text column for step {step_nb_raw} in {docket_number}: {link_ex}")
                        files_number = 1 # Assume at least 1 file if downloadable but link counting failed
                else:
                    files_number = 0 # Not downloadable, so 0 files
 
                # Parse date safely
                try:
                    # Handle potential variations in date format if necessary
                    step_date_filed = pd.to_datetime(date_filed_str).date()
                except ValueError:
                    step_date_filed = None # Handle cases where date parsing fails
                    log_message(f"Warning: Could not parse date '{date_filed_str}' for step {step_nb_raw} in {docket_number}")

                raw_steps_data.append({
                    'row_index': row_nb,
                    'step_nb_raw': step_nb_raw,
                    'step_date_filed': step_date_filed,
                    'proceeding_text': proceeding_text,
                    'docket': docket_number,
                    'court': court,
                    'is_downloadable': is_downloadable,
                    'proceeding_text_cn': None, # Placeholder
                    'files_number': files_number, # Calculated or 0/1
                    'files_downloaded': 0, # Placeholder
                    'files_failed': 0 # Placeholder
                })
            except Exception as e:
                 # Log error specific to the row but continue processing other rows
                 log_message(f"Error processing row {row_nb} for {docket_number}: {e}\n{traceback.format_exc()}")
                 continue # Skip row on error

        if not raw_steps_data:
             log_message(f"No steps data extracted for {docket_number} from {court} after processing rows.")
             empty_df_columns = [
                 'row_index', 'step_nb_raw', 'step_date_filed', 'proceeding_text', 'docket', 'court',
                 'is_downloadable', 'proceeding_text_cn', 'files_number', 'files_downloaded', 'files_failed',
                 'step_nb', 'priority', 'priority_name'
             ]
             return pd.DataFrame(columns=empty_df_columns), True

        steps_df = pd.DataFrame(raw_steps_data)

        # Assign priorities using the new function
        steps_df = assign_document_priorities(steps_df, docket_number, court)

        # Apply step number filling logic (operates on 'step_nb_raw', creates/updates 'step_nb')
        steps_df['step_nb'] = steps_df['step_nb_raw'] # Copy raw numbers first
        steps_df = fill_missing_step_numbers(steps_df) # Now fill/format 'step_nb'

        # Add priority_name column
        steps_df['priority_name'] = steps_df['priority'].map(PRIORITY_NAMES).fillna("PRIORITY_OTHER")

        log_message(f"Successfully parsed {len(steps_df)} steps with new structure for {docket_number} from {court}.")
        return steps_df, True

    except Exception as e:
        # Catch broader errors like table not found
        log_message(f"Error in get_case_steps for {docket_number} from {court}: {e}")
        log_message(f"Traceback:\n{traceback.format_exc()}")
        return pd.DataFrame(), False


# compiled_download_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in download_patterns]
# compiled_no_download_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in no_download_patterns]
# def assess_download_requirement(is_downloadable_flag, step_description):
#     """ Assesses if a step likely requires downloading based on keywords. """
#     if not is_downloadable_flag:
#         return False

#     # Check for no-download patterns first (more specific)
#     if any(pattern.search(step_description) for pattern in compiled_no_download_patterns):
#         return False

#     # Check for download patterns
#     if any(pattern.search(step_description) for pattern in compiled_download_patterns):
#         return True

#     # Default: If downloadable but no keywords match, assume not required for IP search
#     # Returning False aligns with minimizing downloads unless explicitly matched.
#     return False


def fill_missing_step_numbers(df_steps):
    """ Fills missing or duplicate step numbers using decimals. Modifies DataFrame in place. """
    # Ensure 'step_nb' column exists
    if 'step_nb' not in df_steps.columns:
        log_message("Error: 'step_nb' column not found in DataFrame for fill_missing_step_numbers.")
        if 'step_nb_raw' in df_steps.columns:
             df_steps['step_nb'] = df_steps['step_nb_raw']
             log_message("Initialized 'step_nb' from 'step_nb_raw'.")
        else:
             log_message("Cannot initialize 'step_nb'. Aborting fill.")
             return df_steps # Cannot proceed

    # Convert step_nb to string initially to handle various inputs safely
    df_steps['step_nb'] = df_steps['step_nb'].astype(str)

    previous_step_nb_decimal = Decimal('-1.00') # Use Decimal, start distinct from valid steps
    increment = Decimal('0.01')
    new_step_nbs = [] # Store new values to assign at the end

    for index, row in df_steps.iterrows():
        current_step_nb_str = row['step_nb'].strip()
        proceeding_text = row['proceeding_text'] if pd.notna(row['proceeding_text']) else ""
        current_step_nb_decimal = None
        is_current_int_like = False # Represents if it looks like an integer (e.g., "5", "5.00")
        current_int_val = -1

        # Try to interpret the current step number string
        try:
            # Handle potential existing decimals like '1.00' or integers '1'
            temp_decimal = Decimal(current_step_nb_str)
            # Check if it represents an integer value
            if temp_decimal == temp_decimal.to_integral_value(rounding=ROUND_HALF_UP):
                 is_current_int_like = True
                 current_int_val = int(temp_decimal.to_integral_value(rounding=ROUND_HALF_UP))
                 current_step_nb_decimal = Decimal(current_int_val).quantize(Decimal('0.00')) # Store as X.00
            else:
                 # It's a genuine decimal like 1.01, treat as non-integer for comparison logic
                 is_current_int_like = False
                 current_step_nb_decimal = temp_decimal.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

        except Exception: # Handles non-numeric strings, NaN, empty strings etc.
            is_current_int_like = False
            current_step_nb_decimal = None # Mark as uninterpretable numerically

        # Decision Logic
        # When should we generate a new decimal step number?
        # 1. If current step_nb is not interpretable as a number (current_step_nb_decimal is None).
        # 2. If current step_nb looks like an integer BUT it's the *same* integer as the previous step's base integer.
        should_generate_new = False
        if current_step_nb_decimal is None:
            should_generate_new = True
        elif is_current_int_like:
            # Compare integer part of current with integer part of previous
            if current_int_val == int(previous_step_nb_decimal.to_integral_value(rounding=ROUND_HALF_UP)):
                 # Only generate if the previous wasn't already a generated decimal from the *same* base integer
                 if previous_step_nb_decimal == previous_step_nb_decimal.to_integral_value(rounding=ROUND_HALF_UP):
                      should_generate_new = True
                 # Example: Prev=5.00, Curr=5 -> Generate 5.01. Prev=5.01, Curr=5 -> Generate 5.02. Prev=4.00, Curr=5 -> Use 5.00.

        # Generate or Assign Step Number
        if should_generate_new:
            extracted_nb = extract_step_number(proceeding_text) # Try extracting from text
            if extracted_nb is not None:
                 # If extracted matches previous integer part, generate decimal increment from previous
                 if extracted_nb == int(previous_step_nb_decimal.to_integral_value(rounding=ROUND_HALF_UP)):
                     new_step_nb = (previous_step_nb_decimal + increment).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
                 else: # Extracted is a new integer, start fresh for this integer
                     new_step_nb = Decimal(extracted_nb).quantize(Decimal('0.00'))
                     # Safety check: if this new integer is somehow the same as the previous base, increment anyway
                     if new_step_nb == previous_step_nb_decimal.to_integral_value(rounding=ROUND_HALF_UP):
                          new_step_nb = (previous_step_nb_decimal + increment).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
            else: # Cannot extract, just increment from the previous step number recorded
                new_step_nb = (previous_step_nb_decimal + increment).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

            new_step_nbs.append(str(new_step_nb))
            previous_step_nb_decimal = new_step_nb # Update previous with the generated value
        else:
            # Use the existing number (either integer-like as X.00 or existing decimal Y.XX)
            formatted_step_nb = str(current_step_nb_decimal.quantize(Decimal('0.00') if is_current_int_like else Decimal('0.01'), rounding=ROUND_HALF_UP))
            new_step_nbs.append(formatted_step_nb)
            previous_step_nb_decimal = current_step_nb_decimal # Update previous with the current value

    # Assign the collected new step numbers back to the DataFrame
    df_steps['step_nb'] = new_step_nbs
    return df_steps


def extract_step_number(text):
    """ Extracts the first plausible step number from proceeding text. """
    if not isinstance(text, str):
        return None
    # Try matching common patterns for docket entry numbers
    # Prioritize patterns that are less likely to match other numbers
    patterns = [
        r'Docket Entry\s+#?\s*(\d+)', # "Docket Entry 5", "Docket Entry #5"
        r'\(#\s*(\d+)\)',             # "(# 5)"
        r'ECF\s+No\.?\s+(\d+)',       # "ECF No. 5", "ECF No 5"
        r'Doc\.?\s+#?\s*(\d+)',       # "Doc 5", "Doc. #5"
        r'Entry\s+No\.?\s+(\d+)',     # "Entry No. 5"
        r'#\s*(\d+)',                 # "# 5" (Lower priority)
    ]
    for pattern in patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            try:
                num_str = match.group(1)
                # Basic sanity check for unusually large numbers
                if len(num_str) < 5: # Assume step numbers are typically not huge
                    return int(num_str)
            except (ValueError, IndexError):
                continue # Should not happen with \d+ but safety first
    return None # Return None if no pattern matches

# Removed is_integer as it's implicitly handled in fill_missing_step_numbers logic



# def test_search_in_steps(steps_df, max_examples_per_pattern=15):
#     """
#     Searches for answer/motion steps matching specific patterns in the steps DataFrame.
    
#     Args:
#         steps_df: DataFrame containing case steps
#         max_examples_per_pattern: Maximum number of examples to show per pattern (default=15)
#     """
#     # Split the regex into individual patterns for clearer results
#     # patterns = [
#     #     "answer .* by defendant .* complaint",
#     #     "answer .* complaint .* by defendant",
#     #     "motion .* by defendant .* complaint",
#     #     "motion .* complaint .* by defendant ",
#     #     "answer .* by defendant .* temporary restraining order",
#     #     "answer .* temporary restraining order .* by defendant",
#     #     "motion .* by defendant .* temporary restraining order",
#     #     "motion .* temporary restraining order .* by defendant",
#     #     "answer .* by defendant .* preliminary injunction",
#     #     "answer .* preliminary injunction .* by defendant",
#     #     "motion .* by defendant .* preliminary injunction",
#     #     "motion .* preliminary injunction .* by defendant",
        
#     #     "response .* by defendant .* complaint",
#     #     "response .* complaint .* by defendant",
#     #     "response .* by defendant .* temporary restraining order",
#     #     "response .* temporary restraining order .* by defendant",
#     #     "response .* by defendant .* preliminary injunction",
#     #     "response .* preliminary injunction .* by defendant",
#     # ]
    
#     patterns = [
#         ("answer", "to.*complaint"),
#         ("response", "temporary restraining order"),
#         ("response", "preliminary injunction")
#     ]
    
#     print("Searching for answer and motion steps...\n")
    
#     total_matches = 0
#     for star_text, pattern in patterns:
#         # Compile regex pattern
#         regex = re.compile(pattern, re.IGNORECASE)
        
#         # Find matches in proceeding_text
#         matches = steps_df[steps_df['proceeding_text'].str.lower().str.contains(regex, na=False)]
#         matches = matches[matches['proceeding_text'].str.lower().str.startswith(star_text, na=False)] 
        
#         # Sort by docket and step_nb for clearer output
#         matches = matches.sort_values(['step_nb'])
        
#         # Display results for this pattern
#         print(f"\nPattern: {pattern}")
#         print(f"Found {len(matches)} matches")
        
#         if len(matches) > 0:
#             print("\nShowing up to", max_examples_per_pattern, "examples:")
#             display_df = matches.head(max_examples_per_pattern)
#             for index, row in display_df.iterrows():
#                 print(f"{row['case_id']} - {row['step_nb']}: {row['proceeding_text'][:150]}")
            
#         total_matches += len(matches)
        
#     print(f"\nTotal matches across all patterns: {total_matches}")

# # Example usage:
# if __name__ == "__main__":
#     from DatabaseManagement.ImportExport import get_table_from_GZ
#     steps_df = get_table_from_GZ("tb_case_steps", force_refresh=False)
#     test_search_in_steps(steps_df)