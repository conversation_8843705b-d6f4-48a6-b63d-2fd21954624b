from Check.RAG.RAG_Copyright import find_most_similar_copyrights
import os
from FileManagement.Tencent_COS import async_upload_file_with_retry
from AI.LLM_shared import get_json, get_report
from AI.GC_VertexAI import vertex_genai_multi_async
from AI.GCV_GetImageParts import get_image_parts_async
from langfuse.decorators import observe, langfuse_context
import asyncio
import time
from Check.Do_Check_Download import download_from_url
import concurrent.futures
import json
import shutil

# @observe()
# @profile
async def check_one_copyright(temp_dir, check_id, result, plaintiff_df, client, bucket, report_prompt):
    """
    Checks a single copyright result for infringement.
    """
    plaintiff_id = plaintiff_df.loc[plaintiff_df['plaintiff_name'] == result['plaintiff_name'], 'id'].iloc[0]

    upload_task = asyncio.create_task(
        async_upload_file_with_retry(
            client=client,
            bucket=bucket,
            key=f"checks/{check_id}/query/{os.path.basename(result['query_image_path'])}",
            file_path=result["query_image_path"]
        )
    )

    IP_Url = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{plaintiff_id}/high/{result['filename']}"
    ip_file_local = os.path.join(temp_dir, result['filename'])
    download_status = await download_from_url(IP_Url, ip_file_local)
    if not download_status:
        os.makedirs(os.path.join(os.getcwd(), "Errors"), exist_ok=True)
        with open(os.path.join(os.getcwd(), "Errors", "IP_Download_Error.txt"), "a") as f:
            f.write(f"{check_id} - {IP_Url}\n")
        return None

    prompt_list = [
        ("text", report_prompt),
        ("text", f'\n\nAfter the report, conclude with regards to the risk of infringement with {{"final_answer": "xx"}} where xx is a score between 0 and 10 where 0 is very low risk, and 10 is very high risk.'),
        ("text", f"\n\nCopyright Registered Image from '{result['plaintiff_name']}' with registration number '{result['reg_no']}':"),
        ("image_path", ip_file_local),
        ("text", "\n\nProduct Image:"),
        ("image_path", result["query_image_path"]),
    ]

    Query_URL = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/checks/{check_id}/query/{os.path.basename(result['query_image_path'])}"
    image_url = ["", "", "", IP_Url.replace(" ", "%20").replace("http", "https"), "", Query_URL.replace(" ", "%20").replace("http", "https")]

    ai_answer = await vertex_genai_multi_async(prompt_list, image_url=image_url)
    copyright_risk = get_json(ai_answer)

    try:
        final_answer_score = int(copyright_risk["final_answer"])
    except:
        final_answer_score = 0  # so for "Report not required" we get a score of 0

    if final_answer_score > 0:
        report = get_report(ai_answer, "**1. Image Information:**", "**End of Report**")
        report = "**Copyright Risk Assessment Report**\n\n" + report
        for filename in [result['full_filename'], result['filename']]:
            client.copy_object(
                Bucket=bucket,
                Key=f"checks/{check_id}/results/{filename}",
                CopySource={
                    'Bucket': bucket,
                    'Key': f"plaintiff_images/{plaintiff_id}/high/{filename}",
                    'Region': 'ap-guangzhou'
                }
            )
        await upload_task

        return {
            "type": "copyright",
            "ip_owner": str(result['plaintiff_name']),
            "plaintiff_id": str(plaintiff_id),
            "report": report,
            "risk_level": "高风险" if final_answer_score > 5 else "中风险" if final_answer_score > 2 else "低风险",
            "risk_description": "和其他产权有匹配，并且该产权或产权所有人曾经发起TRO诉讼",
            "ip_image": [str(result['full_filename']), str(result['filename'])],
            "Query_URL": Query_URL
        }
    else:
        await upload_task
        return None

@observe()
async def check_copyrights(client, bucket, temp_dir, check_id, local_product_images, local_ip_images, local_reference_images, plaintiff_df, query_image_urls, use_qdrant=False):

    # 1. Bounding Boxes: Get parts of product pictures
    # We make a copy just for copyright because trademark will also create parts and it should not overwrite the trademark parts
    start_time = time.time()

    for product_image in local_product_images:
        product_image_new = os.path.join(os.path.dirname(product_image), "copyright", os.path.basename(product_image))
        os.makedirs(os.path.dirname(product_image_new), exist_ok=True)
        shutil.copy(product_image, product_image_new)
    print(f"\033[33mCopyright: Copied product images in {time.time() - start_time:.1f} seconds\033[0m")

    start_time = time.time()
    parts_tasks = []
    for product_image in local_product_images:
        Query_URL = query_image_urls[os.path.basename(product_image)]
        image_url = ["", Query_URL.replace(" ", "%20").replace("http", "https")]
        parts_tasks.append(get_image_parts_async("Individual images that might be copyrighted", product_image, image_url=image_url))

    parts_results = await asyncio.gather(*parts_tasks)
    images_parts = [part for sublist in parts_results for part in sublist]
    local_images_parts_paths = [part["path"] for part in images_parts]

    # Exclude images that are less than 6000 pixels or where the max(height/width, width/height) > 2.2
    filtered_image_parts = []
    for part in images_parts:
        try:
            width = int(part["width"])
            height = int(part["height"])
            if width * height >= 6000 and max(width / height, height / width) <= 2.2:
                filtered_image_parts.append(part)
        except (ValueError, KeyError) as e:
            print(f"Error processing image part: {part}. Error: {e}")
            # Decide how to handle errors.  Maybe skip this part, or log it.
            continue  # Skip to the next part

    local_images_parts_paths = [part["path"] for part in filtered_image_parts]
    print(f"\033[33mCopyright: Got parts of product images in {time.time() - start_time:.1f} seconds\033[0m")

    # 2. Check copyright infringement on TRO copyrights
    start_time = time.time()

    if use_qdrant:
        # Use Qdrant for vector search
        from Check.RAG.qdrant_copyright import find_most_similar_copyrights_qdrant
        sim_results = await find_most_similar_copyrights_qdrant(
            query_image_paths=local_images_parts_paths + local_ip_images + local_reference_images + local_product_images,
            check_id=check_id,
            client_id=f"check_{check_id}",
            plaintiff_df=plaintiff_df,
            top_n=10,
            similarity_threshold=0.4
        )
    else:
        # Use original vector search method
        sim_results = find_most_similar_copyrights(
            local_images_parts_paths + local_ip_images + local_reference_images + local_product_images,
            top_n=10,
            similarity_threshold=0.4
        )

    print(f"\033[33mCopyright: Copyright RAG done for {len(local_ip_images+local_reference_images+local_product_images)} pictures and {len(local_images_parts_paths)} parts in {time.time() - start_time:.1f} seconds\033[0m")

    if sim_results:
        start_time = time.time()
        with open(os.path.join(os.getcwd(), "Check", "Prompts", "Report_Copyright.txt"), "r", encoding="utf-8") as f:
            report_prompt = f.read()

        # Concurrently process each copyright check
        copyright_check_tasks = [check_one_copyright(temp_dir, check_id, result, plaintiff_df, client, bucket, report_prompt) for result in sim_results]
        copyright_results = await asyncio.gather(*copyright_check_tasks)

        # Filter out None results (where is_copyright["final_answer"] was not "yes")
        filtered_copyright_results = [r for r in copyright_results if r]

    else:
        filtered_copyright_results = []

    print(f"\033[33m ✅ Copyright: Copyright Report Analysis DONE, for {len(sim_results)} RAG results in {time.time() - start_time:.1f} seconds\033[0m")
    return filtered_copyright_results # not used, but tracked by langfuse
