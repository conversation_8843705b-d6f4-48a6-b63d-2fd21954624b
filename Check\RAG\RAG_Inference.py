print("Loading RAG.py")
import os
import sys
current_directory = os.getcwd()
sys.path.insert(0, current_directory)
import numpy as np
from PIL import Image
from tf_keras import applications  # EfficientNet uses keras == tensorflow, uses cuDNN!  Compatibility list: https://www.tensorflow.org/install/source#gpu
from tf_keras import preprocessing
from tf_keras import backend
from sentence_transformers import SentenceTransformer  # CLIP uses pytorch == transformers
import time
from AI.GC_Credentials import get_gcs_credentials
import tensorflow as tf
import torch # Added for Siglip
import gc
from Check.RAG.siglip_model import SiglipModel # Import the new SiglipModel

# Path to the image directory
model_clipv2 = None
model_siglip = None
model_efficientnet = None
structured_patent_image_array = None 
structured_patent_text_array = None 
structured_copyright_array = None
structured_copyright_array_clipv2 = None

def load_all_models():
    global model_clipv2
    global model_siglip
    global model_efficientnet

    if model_siglip is None:
        print(f"\nLoading Siglip model google/siglip2-large-patch16-512 in RAG.py")
        siglip_config = {
            "vector_size": 1024,
            "model_name_or_path": "google/siglip2-large-patch16-512"
        }
        model_siglip = SiglipModel(model_id="siglip2_large_patch16_512", config=siglip_config)
        model_siglip.load()
        print("Siglip model loaded.")

    # Keep Jina-Clip V2 loading if it's still used elsewhere, or remove if fully replaced.
    # For now, assuming it might be used by other parts not covered in the request, so keeping.
    if model_clipv2 is None:
        print(f"\nLoading SentenceTransformer model jinaai/jina-clip-v2 in RAG.py")

        # Define the custom cache folder for Hugging Face models
        huggingface_cache_dir = os.path.join(os.getcwd(), "data", "Models", "huggingface")

        # Create the Hugging Face cache directory if it doesn't exist
        os.makedirs(huggingface_cache_dir, exist_ok=True)

        try:
            # Attempt to load the model with the custom cache folder
            model_clipv2 = SentenceTransformer('jinaai/jina-clip-v2', trust_remote_code=True, cache_folder=huggingface_cache_dir)

            # !!!! Required on RTX 2080Ti GPU because too old. But not on RTX3060 mobile. Also needed in the Except
            # Move the model to the GPU if available, and ensure it's in float32
            # if torch.cuda.is_available():
            #     model_clipv2 = model_clipv2.to('cuda').float() #KEY CHANGE: Move to GPU and cast
            # else:
            #     model_clipv2 = model_clipv2.float() # Ensure float32 even on CPU

        except AttributeError:
            print("Encountered AttributeError, attempting to clear cache and reload.")
            # Clear the cache for the specific model
            SentenceTransformer('jinaai/jina-clip-v2', trust_remote_code=True, cache_folder=None)
            # Re-attempt to load the model with the custom cache folder
            model_clipv2 = SentenceTransformer('jinaai/jina-clip-v2', trust_remote_code=True, cache_folder=huggingface_cache_dir)

    if model_efficientnet is None:
        print("Loading EfficientNet (Keras) model in RAG.py")

        # Float16 yielded bad results when tried on EfficientNetV2L
        # mixed_precision.set_global_policy('mixed_float16')

        # Define the custom cache folder for Keras models
        keras_cache_dir = os.path.join(os.getcwd(), "data", "Models", "keras")

        # Create the Keras cache directory if it doesn't exist
        os.makedirs(keras_cache_dir, exist_ok=True)

        # Set the environment variable for Keras cache
        # os.environ['KERAS_HOME'] = keras_cache_dir

        # Load the EfficientNetB7 model (it will now use the custom cache location)
        model_efficientnet = applications.EfficientNetB7(weights='imagenet', include_top=False, pooling='avg')
        # model_efficientnet2 = applications.EfficientNetV2L(weights='imagenet', include_top=False, pooling='avg', input_shape=(480, 480, 3))

    try:
        global structured_patent_text_array
        structured_patent_text_array = load_embeddings_npy('EmbeddingsPatentTexts.npy')
    except Exception as e:
        print(f"Error loading EmbeddingsPatentTexts.npy: {e}")

    try:
        global structured_patent_image_array
        structured_patent_image_array = load_embeddings_npy('EmbeddingsPatentImages.npy')
    except Exception as e:
        print(f"Error loading EmbeddingsPatentImages.npy: {e}")

    try:
        global structured_copyright_array
        structured_copyright_array = load_embeddings_npy('EmbeddingsCopyright.npy')
    except Exception as e:
        print(f"Error loading EmbeddingsCopyright.npy: {e}")


def load_embeddings_npy(embeddings_file):
   """Loads embeddings from a file or downloads them if the file does not exist."""
   file_path = os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', embeddings_file)
   if os.path.exists(file_path):
       structured_array = np.load(file_path, allow_pickle=True)
   else:
       import requests
       print(f"Downloading {embeddings_file} from http://www.dinsightsgroup.com/")
       url = f'http://www.dinsightsgroup.com/{embeddings_file}'
       response = requests.get(url)
       with open(file_path, 'wb') as f:
           f.write(response.content)
       structured_array = np.load(file_path, allow_pickle=True)
   return structured_array



def get_clipv2_embeddings(data_list, type, batch_size=4):
    print(f"🔥 Getting clipv2 embeddings for {len(data_list)} {type}")
    if not model_clipv2:
        load_all_models()
  
    data_list_ready = []
    if type == "image":
        for image_path in data_list:
            with Image.open(image_path) as img:
                data_list_ready.append(img.copy())  # Copy image data before closing file
    else:
        data_list_ready = data_list
        
    # Batch processing with memory management
    embeddings = []
    
    while batch_size >= 1:
        try:
            for i in range(0, len(data_list_ready), batch_size):
                batch = data_list_ready[i:i+batch_size]
                embeddings.append(model_clipv2.encode(batch))
            break
        except RuntimeError as e:
            if 'CUDA out of memory' in str(e):
                print(f"OOM error with batch size {batch_size}, reducing...")
                batch_size = batch_size // 2
                import torch
                torch.cuda.empty_cache()
            else: raise
    
    # If still failing, try CPU fallback
    if batch_size < 1:
        print("Falling back to CPU")
        cpu_model = SentenceTransformer('jinaai/jina-clip-v2', device='cpu')
        embeddings = [cpu_model.encode(data_list_ready)]
        del cpu_model  # Clean up temporary model
    
    embeddings = np.concatenate(embeddings, axis=0)
    
    if type == "image":
        for img in data_list_ready:
            img.close()
        
    return embeddings


def get_siglip_embeddings(data_list, data_type="image", batch_size=4):
    print(f"🔥 Getting Siglip embeddings for {len(data_list)} {data_type}(s)")
    if not model_siglip:
        load_all_models()

    all_embeddings_list = []
    
    # Batch processing
    current_batch_size = batch_size
    while current_batch_size >= 1:
        all_embeddings_list = [] # Reset for each batch size attempt
        try:
            for i in range(0, len(data_list), current_batch_size):
                batch = data_list[i:i+current_batch_size]
                # The SiglipModel class handles image loading internally if paths are passed for images
                # For text, it expects a list of strings.
                batch_embeddings = model_siglip.compute_features(batch, data_type=data_type)
                all_embeddings_list.append(batch_embeddings)
            break # Success
        except RuntimeError as e:
            if 'CUDA out of memory' in str(e) or 'out of memory' in str(e).lower():
                print(f"OOM error with Siglip batch size {current_batch_size} for {data_type}, reducing...")
                current_batch_size = current_batch_size // 2
                if model_siglip.device.type == 'cuda':
                    torch.cuda.empty_cache()
                gc.collect()
            else:
                raise e # Re-raise other runtime errors
    if not all_embeddings_list: # Failed even with batch size 1
         raise RuntimeError(f"Failed to compute Siglip embeddings for {data_type} even with batch size 1.")
    embeddings = np.concatenate(all_embeddings_list, axis=0)
    return embeddings


def preprocess_image(image_path, res=480):
    """Preprocess the image to match EfficientNetB7 input requirements."""
    with Image.open(image_path).convert('RGB') as img:  # Context manager
        img = img.resize((res, res))
        x = preprocessing.image.img_to_array(img)
    x = np.expand_dims(x, axis=0)
    x = applications.efficientnet.preprocess_input(x)
    return x

# Batching image processing in tensor flow for speed
def get_efficientnet_embeddings(image_paths, res=480, batch_size=4):
    print(f"🔥 Getting efficientnet embeddings for {len(image_paths)} images")
    """Generate embeddings for a batch of images."""
    # RAM: This lines consumes 4.2mb per image (600*600*3), 2.5mb per image (480*480*3)
    # time_start = time.time()
    preprocessed_images = [preprocess_image(image_path, res) for image_path in image_paths]
    # time_end = time.time()
    # print(f"Time taken to preprocess images: {time_end - time_start:.1f} seconds")
    # This line consumes the same amount again. Why?
    x = np.concatenate(preprocessed_images, axis=0)  # Combine into a batch

    del preprocessed_images
    
    if not model_efficientnet:
        load_all_models()

    # Batch processing with memory management
    embeddings = []
    
    while batch_size >= 1:
        embeddings = []  # Reset embeddings for each attempt
        try:
            # Process in batches - exceptions MUST occur at this level
            for i in range(0, len(x), batch_size):
                batch = x[i:i+batch_size]
                embeddings.append(model_efficientnet.predict(batch))
            break  # Success - exit loop
        except tf.errors.ResourceExhaustedError as e:  # Specific error class
            print(f"OOM error with batch size {batch_size}, reducing...")
            batch_size = batch_size // 2
            backend.clear_session()
            gc.collect()  # Add garbage collection
        except Exception as e:
            raise  # Propagate other errors
            
    # If still failing, try CPU fallback
    if batch_size < 1:
        print("Falling back to CPU")
        original_devices = backend.get_epsilon()
        backend.set_epsilon('/CPU:0')  # Alternative device setting
        try:
            embeddings = [model_efficientnet.predict(x)]
        finally:
            backend.set_epsilon(original_devices)
    
    return np.concatenate(embeddings, axis=0)


def get_efficientnet_embeddings2(image_paths):
    if not model_clipv2:
        load_all_models()
  
    embeddings = model_clipv2.encode([Image.open(image) for image in image_paths])
    return embeddings


def get_google_multimodal_embeddings(image_paths):
    import vertexai
    from vertexai.vision_models import Image, MultiModalEmbeddingModel
    
    # Initialize with service account credentials
    credentials = get_gcs_credentials()
    vertexai.init(
        project=os.environ.get("GOOGLE_CLOUD_PROJECT"),  # Hardcoded project ID from GC_VertexAI.py
        location=os.environ.get("GOOGLE_CLOUD_LOCATION", "us-central1"),
        credentials=credentials
    )

    model = MultiModalEmbeddingModel.from_pretrained("multimodalembedding@001")
    # image = Image.load_from_file("gs://cloud-samples-data/vertex-ai/llm/prompts/landmark1.png")
    image = Image.load_from_file(image_paths[0])


    embeddings = model.get_embeddings(
        image=image,
        # contextual_text="Colosseum",
        # dimension=1408,
    )

    # print(f"Image Embedding: {embeddings.image_embedding}")
    # print(f"Text Embedding: {embeddings.text_embedding}")

    return embeddings.image_embedding


# Function to find the most similar, trying to be universal, not used
# def find_most_similar(query_image_paths, top_n=1, similarity_threshold=0.8, structured_array=structured_copyright_array, get_embeddings_func=get_efficientnet_embeddings, get_embeddings_args={}):
#     """
#     Find the most similar image(s) to the query image in the embeddings database.
    
#     Args:
#         query_image_paths (list[str]): Paths to the query images.
#         top_n (int): Number of top similar images to return.
#         similarity_threshold (float): Minimum cosine similarity score to consider a match.
        
#     Returns:
#         List[Tuple[str, float]]: List of (image_id, similarity_score) for the top matches.
#     """

#     if len(query_image_paths) == 0:
#         return []
    
#     # Get embedding for the query image
#     query_embeddings = get_embeddings_func(query_image_paths, **get_embeddings_args)

#     # Ensure query_embeddings is always 2D
#     if query_embeddings.ndim == 1:
#         query_embeddings = query_embeddings.reshape(1, -1)

#     # Extract embeddings from the structured array
#     embeddings = np.array([entry['embedding'] for entry in structured_array])

#     # Compute cosine similarity between all query embeddings and all stored embeddings
#     similarities = cosine_similarity(query_embeddings, embeddings)

#     all_top_matches = []

#     for i, similarity_scores in enumerate(similarities):
#         # Sort results by similarity score in descending order
#         sorted_indices = np.argsort(similarity_scores)[::-1]
#         top_matches = []

#         for idx in sorted_indices[:top_n]:
#             if similarity_scores[idx] >= similarity_threshold:
#                 match_info = {
#                     'query_image_path': query_image_paths[i],
#                     'filename': structured_array[idx]['image_id'],
#                     'similarity': similarity_scores[idx],
#                     'plaintiff_name': structured_array[idx]['plaintiff_name'],
#                     'docket': structured_array[idx]['docket'],
#                     'number_of_cases': structured_array[idx]['number_of_cases']
#                 }
#                 top_matches.append(match_info)

#         all_top_matches.extend(top_matches)
    
#     return all_top_matches


def test_speed():
    # CPU: 50 images in 1 go (no batching) is 26 seconds, batch of 4: 24 sec, batch of 8: 24 sec, batch of 16: 23 sec, 50 images one by one is 140 seconds => batch of 4 is best
    # GPU: 50 images in 1 go (batch of 8) is 4 seconds, 50 images one by one is 8.5 seconds
    # GPU: 100 images batch of 8 is 8 seconds, bach of 4 is 8 seconds, batch of 2 is 13 seconds, 100 images one by one is 20 seconds (60ms per image) => batch of 4 is best
    # GPU: batch of 16 raises ResourceExhaustedError. Only 2Gb of VRAM was available.
    # !!! On GPU: The first image is very long (9 seconds) because it is loading cuDNN

    # Conclusion: batch of 4 is best for GPU and CPU, GPU is 24/4 = 6x faster than CPU

    # GPU comparison on 100 pics batch of 4: 
    # - 5 sec on RTX 2080Ti, 
    # - 7.3 (10.2 sec when hot) on RTX 3060 mobile
    # - 7.09 on RTX 3070 without the 2 libraries, then 6.72 with xformer then 6.9 with flash-attn => library only impact torch, not tensorflow

    folder_path = os.path.join(os.getcwd(), "data", "EvidencesJanFeb")
    image_paths = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp'))]
    image_paths = image_paths[:100]

    embeddings = get_efficientnet_embeddings(image_paths[:1]) # to get cuDNN loaded

    start_time = time.time()
    embeddings = get_efficientnet_embeddings(image_paths, batch_size=4)
    end_time = time.time()
    print(f"Total time taken batch size 4: {end_time - start_time:.1f} seconds")

    print(f"Embeddings shape: {embeddings.shape}")

def test_speed_clipv2():
    # CPU: 50 images in 1 go is 239 seconds, 50 images one by one is 211 seconds, tried with batch of 4 and 8: makes no difference
    # GPU: 50 images in 1 go is 97 seconds, batch of 2: 8.5 sec, batch of 4: 8 sec, batch of 8: 11 sec, batch of 16: 55 sec, 50 images one by one is 10 seconds
    # GPU: 100 images in 1 go is 176 seconds, 100 images one by one is 24-38 seconds
    # GPU: 100 images (2nd run) in 1 go is 270 seconds, 100 images one by one is 36 seconds

    # Conclusion: batch of 4 is best for GPU, GPU is 220/8 = 28x faster than CPU

    # GPU  comparison on 100 pics batch of 4: 
    # - 11.8 sec (2080 TI) , 
    # - 10.5 sec (then 14.7 sec when hot) on RTX 3060 mobile (only 2GB VRAM available)
    # - 9.77 sec (3070) then 6 sec with Xformer, then 6 sec with flash-attn

    folder_path = os.path.join(os.getcwd(), "data", "EvidencesJanFeb")
    image_paths = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp'))]
    image_paths = image_paths[:100]

    embeddings = get_clipv2_embeddings(image_paths[:1], "image") # Needed? 

    start_time = time.time()
    embeddings = get_clipv2_embeddings(image_paths, "image", batch_size=4)
    end_time = time.time()
    print(f"Total time taken batch size 4: {end_time - start_time:.1f} seconds")

    print(f"Embeddings shape: {embeddings.shape}")


if __name__ == "__main__":
    structured_patent_text_array = load_embeddings_npy('EmbeddingsPatentTexts.npy')
    time_start = time.time()
    load_all_models()
    time_end = time.time()
    print(f"Time taken to load all models: {time_end - time_start:.1f} seconds")
    # test_speed()
    test_speed_clipv2()
    # get_efficientnet_embeddings(["D:/Win10User/Downloads/crop.jpg"])