"""
Qdrant-based implementation for patent similarity search.
This module provides functions to find similar patents using Qdrant vector database.
"""

import os
import aiohttp
import numpy as np
from typing import List, Dict, Any
from dotenv import load_dotenv
from langfuse.decorators import observe

# Import the embedding functions from RAG_Inference
from Check.RAG.RAG_Inference import get_clipv2_embeddings

# Load environment variables
load_dotenv(os.path.join(os.getcwd(), "Qdrant", ".env"))

# Get Qdrant API details from environment variables
QDRANT_API_URL = os.getenv("QDRANT_API_URL")
QDRANT_API_KEY = os.getenv("QDRANT_API_KEY")

@observe()
async def find_most_similar_patent_image_qdrant(
    query_image_paths: List[str],
    check_id: str,
    client_id: str,
    plaintiff_df=None,
    top_n: int = 1,
    similarity_threshold_images: float = 0.6,
    similarity_threshold_text: float = 0.25
) -> List[Dict[str, Any]]:
    """
    Find the most similar patents using Qdrant vector database.
    
    Args:
        query_image_paths (List[str]): Paths to the query images.
        check_id (str): The identifier for this specific check/batch.
        client_id (str): The identifier for the client submitting the batch.
        plaintiff_df: DataFrame containing plaintiff information.
        top_n (int): Number of top similar images to return.
        similarity_threshold_images (float): Minimum cosine similarity score to consider a match for images.
        similarity_threshold_text (float): Minimum cosine similarity score to consider a match for text.
        
    Returns:
        List[Dict[str, Any]]: List of match information for the top matches.
    """
    if len(query_image_paths) == 0:
        return []
    
    # Generate embeddings for query images
    clipv2_embeddings = get_clipv2_embeddings(query_image_paths, "image")
    
    # Prepare the request payload for the forward_check endpoint
    products = []
    for i, path in enumerate(query_image_paths):
        products.append({
            "id": f"{os.path.basename(path)}",
            "image_clip": clipv2_embeddings[i].tolist(),
            "image_efficientnet": [0.0] * 2560  # Dummy embedding for EfficientNet (not used for patents)
        })
    
    payload = {
        "client_id": client_id,
        "check_id": check_id,
        "products": products
    }
    
    # Make the API request to the forward_check endpoint
    async with aiohttp.ClientSession() as session:
        async with session.post(
            f"{QDRANT_API_URL}/forward_check",
            json=payload,
            headers={
                "Authorization": f"Bearer {QDRANT_API_KEY}",
                "Content-Type": "application/json"
            }
        ) as response:
            if response.status != 200:
                print(f"Error from Qdrant API: {response.status}")
                return []
            
            result = await response.json()
    
    # Process the results
    all_matches = []
    
    for product_result in result.get("results", []):
        product_id = product_result.get("input_product_id")
        query_image_path = next((path for path in query_image_paths if os.path.basename(path) == product_id), None)
        
        if not query_image_path:
            continue
        
        for infringement in product_result.get("potential_infringements", []):
            if infringement.get("ip_type") != "Patent":
                continue
            
            metadata = infringement.get("metadata", {})
            
            # Convert plaintiff_ids to plaintiff_name if plaintiff_df is provided
            plaintiff_name = ""
            if plaintiff_df is not None and "plaintiff_ids" in metadata:
                plaintiff_ids = metadata.get("plaintiff_ids", [])
                if plaintiff_ids and len(plaintiff_ids) > 0:
                    plaintiff_id = plaintiff_ids[0]
                    plaintiff_name = plaintiff_df.loc[plaintiff_df['id'] == plaintiff_id, 'plaintiff_name'].iloc[0] if not plaintiff_df.loc[plaintiff_df['id'] == plaintiff_id].empty else ""
            
            match_info = {
                "query_image_path": query_image_path,
                "text": metadata.get("patent_title", ""),
                "patent_number": metadata.get("reg_no", ""),
                "filename": metadata.get("reg_no", ""),
                "full_filename": metadata.get("reg_no", ""),
                "similarity": f"{infringement.get('score', 0):.2f}",
                "plaintiff_name": plaintiff_name or metadata.get("applicant", ""),
                "docket": metadata.get("TRO", ""),
                "number_of_cases": len(metadata.get("plaintiff_ids", [])) if "plaintiff_ids" in metadata else 0
            }
            
            all_matches.append(match_info)
    
    # Sort all matches by similarity
    all_matches.sort(key=lambda x: float(x["similarity"]), reverse=True)
    
    # Apply filtering based on similarity threshold
    filtered_matches = [match for match in all_matches if float(match["similarity"]) >= similarity_threshold_images]
    
    # Limit to top_n results
    return filtered_matches[:top_n]
