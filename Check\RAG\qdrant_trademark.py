"""
Qdrant-based implementation for trademark similarity search.
This module provides functions to find similar trademarks using Qdrant vector database.
"""

import os
import aiohttp
import numpy as np
from typing import List, Dict, Any
from dotenv import load_dotenv
from langfuse.decorators import observe

# Import the embedding functions from RAG_Inference
from Check.RAG.RAG_Inference import get_efficientnet_embeddings

# Load environment variables
load_dotenv(os.path.join(os.getcwd(), "Qdrant", ".env"))

# Get Qdrant API details from environment variables
QDRANT_API_URL = os.getenv("QDRANT_API_KEY")
QDRANT_API_KEY = os.getenv("QDRANT_API_KEY")

@observe()
async def find_most_similar_trademark_logo_qdrant(
    product_image_path: str,
    check_id: str,
    client_id: str,
    plaintiff_df=None,
    plaintiff_id=None,
    top_n: int = 1,
    similarity_threshold: float = 0.8
) -> Dict[str, Any]:
    """
    Find the most similar trademark logo using Qdrant vector database.
    
    Args:
        product_image_path (str): Path to the product image.
        check_id (str): The identifier for this specific check/batch.
        client_id (str): The identifier for the client submitting the batch.
        plaintiff_df: DataFrame containing plaintiff information.
        plaintiff_id: Optional plaintiff ID to filter results.
        top_n (int): Number of top similar images to return.
        similarity_threshold (float): Minimum cosine similarity score to consider a match.
        
    Returns:
        Dict[str, Any]: Match information for the top match, or None if no match is found.
    """
    if not os.path.exists(product_image_path):
        print(f"Error: Product image path does not exist: {product_image_path}")
        return None
    
    # Generate embeddings for the product image
    efficientnet_embeddings = get_efficientnet_embeddings([product_image_path])
    
    # Prepare the request payload for the forward_check endpoint
    products = [{
        "id": f"{os.path.basename(product_image_path)}",
        "image_clip": [0.0] * 1024,  # Dummy embedding for CLIP (not used for trademarks)
        "image_efficientnet": efficientnet_embeddings[0].tolist()
    }]
    
    payload = {
        "client_id": client_id,
        "check_id": check_id,
        "products": products
    }
    
    # Make the API request to the forward_check endpoint
    async with aiohttp.ClientSession() as session:
        async with session.post(
            f"{QDRANT_API_URL}/forward_check",
            json=payload,
            headers={
                "Authorization": f"Bearer {QDRANT_API_KEY}",
                "Content-Type": "application/json"
            }
        ) as response:
            if response.status != 200:
                print(f"Error from Qdrant API: {response.status}")
                return None
            
            result = await response.json()
    
    # Process the results
    potential_matches = []
    
    for product_result in result.get("results", []):
        product_id = product_result.get("input_product_id")
        
        if product_id != os.path.basename(product_image_path):
            continue
        
        for infringement in product_result.get("potential_infringements", []):
            if infringement.get("ip_type") != "Trademark":
                continue
            
            metadata = infringement.get("metadata", {})
            
            # Filter by plaintiff_id if provided
            if plaintiff_id is not None:
                plaintiff_ids = metadata.get("plaintiff_ids", [])
                if plaintiff_id not in plaintiff_ids:
                    continue
            
            # Convert plaintiff_ids to plaintiff_name if plaintiff_df is provided
            plaintiff_name = ""
            if plaintiff_df is not None and "plaintiff_ids" in metadata:
                plaintiff_ids = metadata.get("plaintiff_ids", [])
                if plaintiff_ids and len(plaintiff_ids) > 0:
                    plaintiff_id = plaintiff_ids[0]
                    plaintiff_name = plaintiff_df.loc[plaintiff_df['id'] == plaintiff_id, 'plaintiff_name'].iloc[0] if not plaintiff_df.loc[plaintiff_df['id'] == plaintiff_id].empty else ""
            
            match_info = {
                "filename": metadata.get("reg_no", ""),
                "full_filename": metadata.get("reg_no", ""),
                "plaintiff_name": plaintiff_name or metadata.get("applicant_name", ""),
                "plaintiff_id": metadata.get("plaintiff_ids", [None])[0] if "plaintiff_ids" in metadata and metadata.get("plaintiff_ids") else None,
                "docket": metadata.get("TRO", ""),
                "number_of_cases": metadata.get("nb_suits", 0),
                "reg_no": metadata.get("reg_no", ""),
                "int_cls_list": metadata.get("int_cls", []),
                "match_count": int(float(infringement.get("score", 0)) * 100),  # Convert score to match count
                "avg_distance": 1.0 - float(infringement.get("score", 0))  # Convert score to distance
            }
            
            potential_matches.append(match_info)
    
    # Sort by match count (descending) then average distance (ascending)
    if potential_matches:
        potential_matches.sort(key=lambda x: (-x["match_count"], x["avg_distance"]))
        
        # Filter by similarity threshold
        if potential_matches[0]["match_count"] / 100 >= similarity_threshold:
            return potential_matches[0]
    
    return None
