import os
import shutil
import argparse
from PIL import Image
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
import cv2
from sklearn.cluster import DBSCAN
from logdata import log_message

# Parameters
SIMILARITY_THRESHOLD = 0.50
TEXT_THRESHOLD = 100  # character count

model = None
preprocess = None

def ensure_folder(folder):
    if not os.path.exists(folder):
        os.makedirs(folder)

def get_all_images(folder):
    return [os.path.join(folder, f) for f in os.listdir(folder)
            if f.lower().endswith((".jpg", ".jpeg", ".png"))]

def extract_feature_vector(image_path):
    import torch
    from torchvision import models, transforms
    
    global model
    if model is None:
        # Load MobileNetV2 (feature extractor only)
        model = models.mobilenet_v2(weights='MobileNet_V2_Weights.IMAGENET1K_V1').features.eval()
        
    global preprocess
    if preprocess is None:
        # Image preprocessing for CNN
        preprocess = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406],
                                std=[0.229, 0.224, 0.225])
        ])

    image = Image.open(image_path).convert('RGB')
    tensor = preprocess(image).unsqueeze(0)
    with torch.no_grad():
        output_tensor = model(tensor)
        squeezed_tensor = output_tensor.squeeze()
        numpy_features = squeezed_tensor.numpy()
    return numpy_features.flatten()

def text_heavy(image_path):
    try:
        image = cv2.imread(image_path)
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Binarize image
        _, thresh = cv2.threshold(gray, 150, 255, cv2.THRESH_BINARY_INV)

        # Dilate to merge text lines
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
        dilated = cv2.dilate(thresh, kernel, iterations=1)

        # Find contours
        contours, _ = cv2.findContours(dilated, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # Count text-like regions
        text_like_contours = [cnt for cnt in contours if cv2.contourArea(cnt) > 100]

        # Heuristic: If too many text-like areas, mark as text-heavy
        if len(text_like_contours) > 15:
            return True

        return False
    except Exception as e:
        print(f"Text-detection failed for {image_path}: {e}")
        return False


def is_collage_or_screenshot(image_path, edge_threshold=100, grid_sensitivity=0.6):
    """
    Detect if an image is a collage based on:
    1. Strong edge detection (collages have many strong edges between sections)
    2. Grid-like structures (many collages use grid layouts)
    3. Color diversity (collages often have distinctly different color regions)
    
    Args:
        image_path: Path to the image file
        edge_threshold: Threshold for edge detection sensitivity
        grid_sensitivity: Higher values make the function more likely to classify as collage
        
    Returns:
        Boolean indicating if the image is likely a collage
    """
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        return False
    
    # Resize for consistency and performance
    max_dimension = 800
    h, w = image.shape[:2]
    if max(h, w) > max_dimension:
        scale = max_dimension / max(h, w)
        image = cv2.resize(image, (int(w * scale), int(h * scale)))
    
    # 1. Edge detection
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    edges = cv2.Canny(gray, edge_threshold, edge_threshold * 2)
    edge_density = np.sum(edges > 0) / (edges.shape[0] * edges.shape[1])
    
    # 2. Grid detection
    # Look for horizontal and vertical lines
    h_lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=100, minLineLength=image.shape[1]//5, maxLineGap=20)
    v_lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=100, minLineLength=image.shape[0]//5, maxLineGap=20)
    
    line_count = 0
    if h_lines is not None:
        line_count += len(h_lines)
    if v_lines is not None:
        line_count += len(v_lines)
    
    # Normalize line count by image size
    normalized_line_count = line_count / ((image.shape[0] + image.shape[1]) / 2)
    
    # 3. Color diversity analysis
    # Downsample image for faster processing
    small = cv2.resize(image, (50, 50))
    pixels = small.reshape(-1, 3).astype(np.float32)
    
    # Cluster colors using DBSCAN
    dbscan = DBSCAN(eps=25, min_samples=5)
    dbscan.fit(pixels)
    labels = dbscan.labels_
    n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
    
    # Normalize cluster count
    max_expected_clusters = 15  # Expected max for a complex image
    normalized_clusters = min(n_clusters / max_expected_clusters, 1.0)
    
    # Combined score
    collage_score = (edge_density * 2 + normalized_line_count + normalized_clusters) / 4
    
    # Detect multi-panel grid pattern
    is_collage = collage_score > grid_sensitivity
    
    return is_collage



def deduplicate_images(input_folder, duplicate_folder):
    ensure_folder(duplicate_folder)
    image_paths = get_all_images(input_folder)
    feature_vectors = {}
    to_move = set()

    log_message(f"Total images before filtering: {len(image_paths)}")

    # Step 1: Remove text-heavy or collage/screenshot images
    # print("Filtering out non-art images...")
    for path in image_paths:
        if is_collage_or_screenshot(path):  #text_heavy(path) or 
            print(f"Flagged as non-art: {path}")
            to_move.add(path)

    # Step 2: Extract features for images not flagged above
    # print("Extracting features...")
    filtered_paths = [p for p in image_paths if p not in to_move]
    for path in filtered_paths:
        try:
            feature_vectors[path] = extract_feature_vector(path)
        except Exception as e:
            print(f"[ERROR] Feature extraction failed for {path}: {e}")
            to_move.add(path)

    # Step 3: Compare feature vectors to find duplicates
    # print("Comparing images for duplicates...")
    image_list = list(feature_vectors.keys())
    for i in range(len(image_list)):
        for j in range(i + 1, len(image_list)):
            if image_list[j] in to_move:
                continue
            sim = cosine_similarity([feature_vectors[image_list[i]]],
                                    [feature_vectors[image_list[j]]])[0][0]
            if sim >= SIMILARITY_THRESHOLD:
                print(f"Duplicate found (score={sim:.3f}): {image_list[i]} <-> {image_list[j]}")
                to_move.add(image_list[j])

    # Step 4: Safely move flagged images
    log_message(f"Total duplicates/non-art to remove: {len(to_move)}")
    for path in to_move:
        dest = os.path.join(duplicate_folder, os.path.basename(path))
        if os.path.exists(path):
            try:
                # print(f"Moving: {path} -> {dest}")
                shutil.move(path, dest)
            except Exception as e:
                print(f"[ERROR] Could not move {path}: {e}")
        # else:
        #     print(f"[SKIP] File already missing or moved: {path}")

# def remove_text_duplicates(input_folder, duplicate_folder):
#     ensure_folder(duplicate_folder)
#     image_paths = get_all_images(input_folder)
#     feature_vectors = {}
#     to_move = set()

#     print(f"Total images before filtering: {len(image_paths)}")

#     # Step 1: Remove text-heavy or collage/screenshot images
#     print("Filtering out non-art images...")
#     for path in image_paths:
#         if text_heavy(path) or is_collage_or_screenshot(path):  
#             print(f"Flagged as non-art: {path}")
#             to_move.add(path)

#     # Step 2: Extract features for images not flagged above
#     print("Extracting features...")
#     filtered_paths = [p for p in image_paths if p not in to_move]
#     for path in filtered_paths:
#         try:
#             feature_vectors[path] = extract_feature_vector(path)
#         except Exception as e:
#             print(f"[ERROR] Feature extraction failed for {path}: {e}")
#             to_move.add(path)

#     # Step 3: Compare feature vectors to find duplicates
#     print("Comparing images for duplicates...")
#     image_list = list(feature_vectors.keys())
#     for i in range(len(image_list)):
#         for j in range(i + 1, len(image_list)):
#             if image_list[j] in to_move:
#                 continue
#             sim = cosine_similarity([feature_vectors[image_list[i]]],
#                                     [feature_vectors[image_list[j]]])[0][0]
#             if sim >= SIMILARITY_THRESHOLD:
#                 print(f"Duplicate found (score={sim:.3f}): {image_list[i]} <-> {image_list[j]}")
#                 to_move.add(image_list[j])

#     # Step 4: Safely move flagged images
#     print(f"Total duplicates/non-art to move: {len(to_move)}")
#     for path in to_move:
#         dest = os.path.join(duplicate_folder, os.path.basename(path))
#         if os.path.exists(path):
#             try:
#                 print(f"Moving: {path} -> {dest}")
#                 shutil.move(path, dest)
#             except Exception as e:
#                 print(f"[ERROR] Could not move {path}: {e}")
#         else:
#             print(f"[SKIP] File already missing or moved: {path}")




#For running standalone script
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Detect and remove duplicate/text-heavy images.")
    parser.add_argument("-i", "--input", required=True, help="Input folder of images")
    parser.add_argument("-o", "--output", default="removed", help="Output folder for duplicates")
    args = parser.parse_args()

    deduplicate_images(args.input, args.output)
